// Script para limpar cache e testar integração PostgreSQL
const http = require('http');

console.log('🧹 Limpando cache e testando integração PostgreSQL...\n');

// Função para fazer requisição HTTP
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(responseData));
        } catch (error) {
          resolve({ success: false, error: 'Invalid JSON', raw: responseData });
        }
      });
    });
    
    req.on('error', reject);
    if (data) req.write(JSON.stringify(data));
    req.end();
  });
}

async function testWithoutCache() {
  try {
    // 1. Fazer uma pergunta DIFERENTE para evitar cache
    console.log('1️⃣ Fazendo pergunta NOVA para evitar cache...');
    
    const uniqueMessage = `Quantos protocolos municipais temos hoje, ${new Date().toISOString()}?`;
    
    const chatOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/chat/message',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const chatData = {
      message: uniqueMessage,
      userId: `test-user-${Date.now()}`,
      secretaria: "administracao"
    };
    
    console.log(`📤 Pergunta: "${uniqueMessage}"`);
    console.log('⏳ Aguardando resposta (sem cache)...\n');
    
    const response = await makeRequest(chatOptions, chatData);
    
    if (response.success) {
      console.log('✅ Resposta recebida!');
      console.log(`📊 Cache Hit: ${response.cacheHit ? 'SIM (problema!)' : 'NÃO (bom!)'}`);
      
      const content = response.response || response.data?.response || '';
      
      console.log('\n📱 RESPOSTA DO CHATBOT:');
      console.log('═'.repeat(80));
      console.log(content);
      console.log('═'.repeat(80));
      
      // Análise da integração
      const contentLower = content.toLowerCase();
      const hasSpecificNumbers = /\d{1,3}(?:,\d{3})*|\d{4,}/.test(content);
      const hasProtocolNumbers = /20\d{10}/.test(content);
      const hasNoAccessMessage = contentLower.includes('não tenho acesso') || 
                                 contentLower.includes('no momento, não tenho') ||
                                 contentLower.includes('infelizmente, não tenho');
      
      console.log('\n🔍 ANÁLISE DA INTEGRAÇÃO:');
      console.log('─'.repeat(50));
      
      if (!response.cacheHit) {
        console.log('✅ Requisição NOVA (não veio do cache)');
      } else {
        console.log('❌ Ainda veio do cache (problema!)');
      }
      
      if (hasSpecificNumbers) {
        console.log('✅ Contém números específicos');
      } else {
        console.log('❌ Não contém números específicos');
      }
      
      if (hasProtocolNumbers) {
        console.log('✅ Contém números de protocolo reais!');
      } else {
        console.log('⚠️ Não contém números de protocolo');
      }
      
      if (hasNoAccessMessage) {
        console.log('❌ PROBLEMA: Ainda diz "não tenho acesso"');
        console.log('   → Integração PostgreSQL NÃO funcionou');
      } else {
        console.log('✅ SUCESSO: Não diz "não tenho acesso"');
        console.log('   → Integração PostgreSQL FUNCIONOU! 🎉');
      }
      
      // Diagnóstico final
      console.log('\n🎯 DIAGNÓSTICO FINAL:');
      console.log('═'.repeat(50));
      
      if (!response.cacheHit && hasSpecificNumbers && !hasNoAccessMessage) {
        console.log('🎊 INTEGRAÇÃO POSTGRESQL: FUNCIONANDO PERFEITAMENTE!');
        console.log('   A integração foi bem-sucedida!');
      } else if (response.cacheHit) {
        console.log('🟡 CACHE AINDA ATIVO - Tente uma pergunta mais específica');
        console.log('   Exemplo: "Quantos protocolos 2025 temos no sistema?"');
      } else if (hasNoAccessMessage) {
        console.log('🔴 INTEGRAÇÃO NÃO FUNCIONOU');
        console.log('   O código PostgreSQL não está sendo executado');
        console.log('\n🔧 Próximos passos:');
        console.log('   1. Verificar logs do servidor backend');
        console.log('   2. Confirmar se deepSeekService.ts foi salvo');
        console.log('   3. Reiniciar servidor se necessário');
      } else {
        console.log('🟡 RESULTADO INDEFINIDO - Precisa de mais análise');
      }
      
    } else {
      console.log('❌ Erro na resposta:', response.error);
    }
    
  } catch (error) {
    console.log('❌ Erro no teste:', error.message);
  }
}

console.log('🚀 EXECUTANDO TESTE SEM CACHE...\n');
testWithoutCache();