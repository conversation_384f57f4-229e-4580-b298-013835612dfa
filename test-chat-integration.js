// Script para testar a integração PostgreSQL no chat
// Execute este comando no diretório raiz: node test-chat-integration.js

const https = require('http');

async function testChatIntegration() {
  console.log('🧪 Testando integração PostgreSQL com o chat...');
  
  // Teste 1: Verificar se o endpoint de saúde PostgreSQL funciona
  console.log('\n1️⃣ Testando endpoint de saúde PostgreSQL...');
  
  try {
    const healthResponse = await makeRequest('GET', 'localhost', 3001, '/api/postgres/health');
    console.log('✅ Endpoint PostgreSQL:', healthResponse.success ? 'FUNCIONANDO' : 'COM ERRO');
    if (healthResponse.data) {
      console.log(`   Status: ${healthResponse.data.status}`);
      console.log(`   Protocolos: ${healthResponse.data.estatisticasRapidas?.protocolos || 'N/A'}`);
    }
  } catch (error) {
    console.log('❌ Endpoint PostgreSQL não acessível:', error.message);
  }
  
  // Teste 2: Testar mensagem de chat sobre protocolos
  console.log('\n2️⃣ Testando mensagem de chat sobre protocolos...');
  
  const chatMessage = {
    message: "Quantos protocolos de alvará temos em andamento?",
    userId: "test-user",
    secretaria: "administracao"
  };
  
  try {
    const chatResponse = await makeRequest('POST', 'localhost', 3001, '/api/chat/message', chatMessage);
    
    if (chatResponse.success) {
      console.log('✅ Chat respondeu com sucesso!');
      console.log('\n📝 Resposta do chatbot:');
      console.log('─'.repeat(60));
      console.log(chatResponse.data.content);
      console.log('─'.repeat(60));
      
      // Verificar se a resposta contém dados específicos
      const response = chatResponse.data.content.toLowerCase();
      const hasSpecificNumbers = /\d{1,3}(?:,\d{3})*|\d+/.test(chatResponse.data.content);
      const hasProtocolNumbers = /20\d{10}/.test(chatResponse.data.content);
      const hasNoAccessMessage = response.includes('não tenho acesso') || response.includes('no momento, não tenho');
      
      console.log('\n🔍 Análise da resposta:');
      if (hasSpecificNumbers) {
        console.log('✅ Contém números específicos (bom sinal!)');
      }
      if (hasProtocolNumbers) {
        console.log('✅ Contém números de protocolo reais (excelente!)');
      }
      if (hasNoAccessMessage) {
        console.log('❌ Ainda contém mensagem "não tenho acesso" (integração não funcionou)');
      } else {
        console.log('✅ Não contém mensagem "não tenho acesso" (integração funcionou!)');
      }
      
      console.log(`\n💰 Custo: $${chatResponse.data.cost?.total || 0}`);
      console.log(`🔤 Tokens: ${chatResponse.data.tokens?.total || 0}`);
      
    } else {
      console.log('❌ Chat falhou:', chatResponse.error);
    }
    
  } catch (error) {
    console.log('❌ Erro no teste de chat:', error.message);
  }
  
  console.log('\n🏁 Teste concluído!');
}

// Função auxiliar para fazer requisições HTTP
function makeRequest(method, hostname, port, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname,
      port,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    };
    
    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }
    
    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve(parsedData);
        } catch (error) {
          resolve({ success: false, error: 'Invalid JSON response', raw: responseData });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Executar teste
testChatIntegration().catch(console.error);