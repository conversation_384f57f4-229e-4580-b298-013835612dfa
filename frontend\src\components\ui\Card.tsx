import { HTMLAttributes, forwardRef } from 'react';
import { clsx } from 'clsx';

interface CardProps extends HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'cost' | 'performance' | 'cache' | 'savings';
}

export const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', children, ...props }, ref) => {
    const variants = {
      default: 'border border-pv-gray-200 bg-white',
      cost: 'bg-gradient-to-br from-pv-yellow-50 to-pv-yellow-100 border-l-4 border-pv-yellow-500',
      performance: 'bg-gradient-to-br from-pv-cyan-50 to-pv-cyan-100 border-l-4 border-pv-cyan-500',
      cache: 'bg-gradient-to-br from-pv-blue-50 to-pv-blue-100 border-l-4 border-pv-blue-primary',
      savings: 'bg-gradient-to-br from-pv-orange-50 to-pv-orange-100 border-l-4 border-pv-orange-500'
    };

    return (
      <div
        ref={ref}
        className={clsx(
          'rounded-lg p-6 shadow-sm transition-all duration-200 hover:shadow-md',
          variants[variant],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

export const CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={clsx('flex flex-col space-y-1.5 pb-4', className)}
      {...props}
    />
  )
);

CardHeader.displayName = 'CardHeader';

export const CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={clsx('text-lg font-semibold leading-none tracking-tight text-pv-gray-800', className)}
      {...props}
    />
  )
);

CardTitle.displayName = 'CardTitle';

export const CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={clsx('', className)} {...props} />
  )
);

CardContent.displayName = 'CardContent';