import { ChatMessage } from '@/types';
import { clsx } from 'clsx';
import { NoSSR } from '@/components/ui/NoSSR';

interface MessageBubbleProps {
  message: ChatMessage;
}

export function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.type === 'user';

  return (
    <div className={clsx('flex w-full mb-4', isUser ? 'justify-end' : 'justify-start')}>
      <div className={clsx('flex max-w-[80%] space-x-3', isUser ? 'flex-row-reverse space-x-reverse' : 'flex-row')}>
        {/* Avatar */}
        <div className={clsx(
          'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
          isUser 
            ? 'bg-pv-blue-primary text-white' 
            : 'bg-pv-cyan-500 text-white'
        )}>
          {isUser ? 'U' : 'IA'}
        </div>

        {/* Message Content */}
        <div className={clsx(
          'flex flex-col space-y-1',
          isUser ? 'items-end' : 'items-start'
        )}>
          <div className={clsx(
            'px-4 py-3 rounded-lg text-sm',
            isUser 
              ? 'bg-gradient-to-r from-pv-blue-primary to-pv-blue-secondary text-white' 
              : 'bg-pv-gray-100 text-pv-gray-800 border border-pv-gray-200'
          )}>
            <p className="whitespace-pre-wrap">{message.content}</p>
          </div>

          {/* Timestamp */}
          <NoSSR fallback={<span className="text-xs text-pv-gray-500">--:--</span>}>
            <span className="text-xs text-pv-gray-500">
              {message.timestamp.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </span>
          </NoSSR>
        </div>
      </div>
    </div>
  );
}