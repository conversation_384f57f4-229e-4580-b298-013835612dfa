# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# PREFEITURA VIRTUAL IA - SISTEMA DE CHATBOT INTELIGENTE

## 🎯 Visão Geral do Projeto
Sistema de IA municipal para Prefeitura de Valparaíso de Goiás com cache inteligente multicamada, controle rigoroso de custos e arquitetura de alta performance. O sistema é otimizado para economizar até 65% dos custos de IA através de cache semântico e horários de desconto.

## 📋 Comandos Essenciais

### Desenvolvimento
```bash
# Instalar todas as dependências (root, backend e frontend)
npm run install:all

# Desenvolvimento completo (backend :3001 + frontend :3000)
npm run dev

# Desenvolvimento individual
cd backend && npm run dev    # Backend com tsx watch
cd frontend && npm run dev   # Frontend Next.js

# Build completo
npm run build               # Build backend + frontend
```

### Testes
```bash
# Executar todos os testes
npm run test                # Testes backend + frontend

# Testes individuais
cd backend && npm run test          # Jest backend
cd backend && npm run test:watch    # Jest watch mode
cd frontend && npm run test         # Jest frontend
cd frontend && npm run test:watch   # Jest watch mode

# Type checking
cd backend && tsc --noEmit          # TypeScript check
cd frontend && npm run type-check   # TypeScript check
```

### Banco de Dados (Prisma)
```bash
cd backend
npm run db:generate    # Gera cliente Prisma
npm run db:push       # Sync schema com DB
npm run db:migrate    # Cria migration
npm run db:studio     # Interface visual Prisma
```

### Testes de API
```bash
# Autenticação
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123","secretaria":"administracao"}'

# Chat com cache
curl -X POST http://localhost:3001/api/chat/message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -d '{"message":"Como fazer solicitação de alvará?","secretaria":"administracao"}'

# Métricas
curl -X GET http://localhost:3001/api/chat/metrics \
  -H "Authorization: Bearer <JWT_TOKEN>"
```

## 🏗️ Arquitetura de Alto Nível

### Sistema de Cache Multicamada
```
┌─────────────────────────────────────────────────┐
│             REQUEST FLOW                         │
├─────────────────────────────────────────────────┤
│ 1. Exact Cache (MD5)     → 100% economia        │
│ 2. Semantic Cache (AI)   → 100% economia        │
│ 3. Context Cache         → Resposta otimizada   │
│ 4. DeepSeek API          → Custo variável       │
└─────────────────────────────────────────────────┘
```

### Filas Inteligentes com Priorização
```
IMMEDIATE QUEUE → Emergências (processamento instantâneo)
NORMAL QUEUE   → Se desconto ativo: processa, senão: aguarda
BATCH QUEUE    → Sempre aguarda horário de desconto (50% economia)
```

### Integração de Bancos de Dados
- **PostgreSQL** (*************:5411): Dados municipais, usuários, estrutura principal
- **MongoDB** (*************:2711): Conversas, logs de chat, histórico
- **Redis** (Dual DB): Cache (DB0) + Filas Bull (DB1)

## 🔐 Segurança e Padrões Críticos

### Regras Invioláveis
1. **NUNCA** usar dados mockados em produção
2. **NUNCA** commitar credenciais (apenas .env)
3. **NUNCA** inserir dados direto no banco (sempre via API)
4. **SEMPRE** validar com Zod schemas
5. **SEMPRE** aplicar rate limiting
6. **SEMPRE** mascarar dados sensíveis nos logs

### Fluxo de Autenticação
```typescript
// Sistema híbrido: mock para dev, PostgreSQL para prod
POST /api/auth/login
{
  "email": "<EMAIL>",  // OU
  "cpf": "12345678900",                   // OU
  "password": "admin123",
  "secretaria": "administracao"
}
```

### Rate Limits Configurados
- Login: 5 tentativas/15min
- Chat: 30 msgs/min
- Dashboard: 50 req/5min
- Admin: 10 ações/30min

## 💰 Sistema de Otimização de Custos

### Horários de Desconto (50% economia)
- **Horário Local**: 13:30 - 21:30 (Brasília)
- **Horário UTC**: 16:30 - 00:30
- **Economia**: $0.00219/msg → $0.001095/msg

### Custos DeepSeek V3
```
Normal:
- Input: $0.07/1M tokens
- Output: $1.10/1M tokens

Desconto (50%):
- Input: $0.035/1M tokens  
- Output: $0.55/1M tokens
```

### Métricas de Performance
- Cache Hit Rate: 60-70%
- Economia Projetada: R$ 540-960/mês
- ROI: 450-800%

## 🛠️ Padrões de Desenvolvimento

### Backend - Estrutura de Controller
```typescript
router.post("/endpoint",
  authenticate,           // JWT validation
  authorize("admin"),     // Role check
  auditAccess("action"),  // Log access
  validateRequest(schema), // Zod validation
  controller             // Business logic
);
```

### Frontend - Container/Presentational Pattern
```typescript
// Container (lógica)
export function FeatureContainer() {
  const logic = useFeatureLogic();
  return <Feature {...presentationalProps} />;
}

// Component (UI apenas)
export function Feature({ data, onAction, isLoading }) {
  return <div>{/* UI */}</div>;
}
```

### Validação com Zod
```typescript
// Sempre criar schemas para todas as rotas
const messageSchema = z.object({
  message: z.string().min(1).max(1000),
  userId: z.string(),
  secretaria: z.enum(['administracao', 'saude', 'educacao']),
  forceImmediate: z.boolean().optional()
});
```

## 📂 Estrutura de Diretórios

```
backend/src/
├── config/         # Configurações (Redis, DB)
├── controllers/    # Route handlers
├── services/       # Lógica de negócio
├── repositories/   # Acesso a dados
├── schemas/        # Validação Zod
├── middleware/     # Auth, cache, rate limit
├── scripts/        # Scripts utilitários
└── types/          # TypeScript types

frontend/src/
├── app/           # Next.js 14 app router
├── containers/    # Smart components
├── components/    # UI components
├── hooks/         # Custom hooks
├── contexts/      # Global state
├── services/      # API clients
└── types/         # TypeScript types
```

## 🔍 Debugging e Monitoramento

### Logs em Tempo Real
```bash
# Sistema
tail -f backend/logs/app-$(date +%Y-%m-%d).log

# Segurança
tail -f backend/logs/security-$(date +%Y-%m-%d).log

# Cache Redis
redis-cli monitor | grep -E "(GET|SET|DEL)"

# Métricas via API
curl -s http://localhost:3001/api/chat/metrics | jq .
```

### Comandos Redis
```bash
# Verificar chaves
redis-cli -n 0 KEYS "*cache*"     # Cache DB
redis-cli -n 1 KEYS "*queue*"     # Queue DB

# Limpar cache específico
redis-cli -n 0 DEL "exact_cache:*"
redis-cli -n 0 DEL "semantic_cache:*"
```

## 📚 Documentação Detalhada

- `/docs/README.md` - Índice completo
- `/docs/redis-architecture.md` - Arquitetura Redis
- `/docs/database-integration-plan.md` - Plano de integração DB
- `/docs/postgres-integration-report.md` - Relatório PostgreSQL

## 🚀 Próximas Implementações

### Em Andamento
- Sistema de autenticação JWT completo
- Integração com dados reais PostgreSQL
- Frontend React completo com chat interface
- Sistema RAG com documentos municipais

### Pendências Críticas
- Implementar error boundaries no frontend
- Adicionar testes de integração
- Configurar CI/CD pipeline
- Documentar APIs com OpenAPI/Swagger

## ⚡ Performance Tips

1. **Cache First**: Sempre verificar cache antes de chamar API
2. **Batch Operations**: Agrupar operações quando possível
3. **Rate Limiting**: Implementar em todas as rotas públicas
4. **Logging**: Usar níveis apropriados (info, warn, error)
5. **Async/Await**: Evitar callbacks, usar promises modernas

## 🔧 Variáveis de Ambiente Essenciais

```bash
# IA
DEEPSEEK_API_KEY=
OPENAI_API_BASE_URL=https://api.deepseek.com

# Bancos
DATABASE_URL=postgresql://...
MONGODB_URI=mongodb://...
REDIS_URL=redis://...

# Auth
JWT_SECRET=
JWT_EXPIRES_IN=24h

# Cache
CACHE_TTL_EXACT=86400
CACHE_TTL_SEMANTIC=43200
DISCOUNT_START_HOUR=16
DISCOUNT_END_HOUR=0
```

## 🤖 Claude-Flow Integration

O projeto agora inclui suporte para Claude-Flow com Node.js 22. Os seguintes recursos estão disponíveis:

### Comandos Claude-Flow
```bash
# Status do sistema
./claude-flow status

# Modos SPARC
./claude-flow sparc modes
./claude-flow sparc run architect "design system"
./claude-flow sparc run code "implement feature"
./claude-flow sparc run tdd "create tests"

# Sistema de memória
./claude-flow memory store key "value"
./claude-flow memory query term
./claude-flow memory list
```

### Modos SPARC Disponíveis
- architect: Design de arquitetura
- code: Desenvolvimento
- tdd: Testes
- security-review: Auditoria de segurança
- integration: Integração
- devops: Deploy/CI-CD
- ask: Pesquisa
- research: Análise profunda
- debug: Debugging
- review: Code review
- optimize: Otimização
- document: Documentação
- migrate: Migração
- refactor: Refatoração
- test: Testes gerais
- analyze: Análise
- design: Design patterns

---

**IMPORTANTE**: Este sistema prioriza economia de custos, segurança e performance. Sempre considere o impacto no cache e nos custos de IA ao fazer modificações.