# 🚀 G<PERSON>a <PERSON>to: Claude-Flow

## 📋 O que é Claude-Flow?

Claude-Flow é uma plataforma de orquestração que permite coordenar múltiplos agentes de IA simultaneamente usando Claude Code. É uma camada de automação que transforma o Claude Code em um sistema de desenvolvimento multi-agente.

**Principais Benefícios:**
- Execução paralela de até 10 agentes especializados
- Memória compartilhada entre todos os agentes
- 17 modos especializados (Architect, Coder, TDD, Security, DevOps, etc.)
- Interface web para monitoramento
- Desenvolvimento automatizado end-to-end

---

## 🛠️ Pré-requisitos

Antes de instalar, certifique-se que você tem:

- **Node.js** (versão 18 ou superior)
- **npm** (vem com Node.js)
- **Claude Code** instalado e configurado
- **Terminal/Prompt de comando** com acesso

### Verificar Pré-requisitos:
```bash
# Verificar Node.js
node --version

# Verificar npm
npm --version

# Verificar Claude Code
claude --version
```

---

## ⚡ Instalação Rápida (Recomendada)

### Método 1: NPX (Mais Rápido)
```bash
# Instalação e inicialização em um comando
npx claude-flow@latest init --sparc

# Isso cria automaticamente:
# ✓ Wrapper local ./claude-flow
# ✓ Diretório .claude/ com configurações
# ✓ Arquivo CLAUDE.md (instruções para Claude Code)
# ✓ 17 modos SPARC pré-configurados
```

### Método 2: Instalação Global
```bash
# Instalar globalmente
npm install -g claude-flow

# Inicializar em qualquer projeto
claude-flow init --sparc
```

### Método 3: Instalação Local (Para Projetos)
```bash
# Adicionar ao projeto
npm install claude-flow --save-dev

# Inicializar
npx claude-flow init --sparc
```

---

## 🎯 Configuração Inicial

Após a instalação, você terá:

### Estrutura de Arquivos Criada:
```
seu-projeto/
├── .claude/
│   └── settings.json        # Configurações otimizadas
├── .roomodes/              # 17 modos SPARC
├── CLAUDE.md              # Instruções para Claude Code
├── claude-flow*           # Wrapper local (executável)
└── node_modules/
```

### Configurações Automáticas Aplicadas:
- ✅ Timeouts estendidos (5-10 minutos)
- ✅ Todas as ferramentas permitidas
- ✅ Suporte a outputs grandes (500KB)
- ✅ Execução paralela habilitada
- ✅ Auto-save na memória ativado

---

## 🎮 Comandos Básicos

### Iniciar o Sistema
```bash
# Iniciar com interface web (recomendado)
./claude-flow start --ui --port 3000

# Iniciar sem interface web
./claude-flow start

# Verificar status do sistema
./claude-flow status
```

### Comandos de Agentes
```bash
# Criar um agente específico
./claude-flow agent spawn researcher --name "DataBot"

# Ver informações de um agente
./claude-flow agent info agent-123

# Terminar um agente
./claude-flow agent terminate agent-123
```

---

## 🧠 Usando os Modos SPARC

### Modos Disponíveis:

| Modo | Função | Uso |
|------|--------|-----|
| `architect` | Design de arquitetura | `./claude-flow sparc run architect "design API"`|
| `code` | Desenvolvimento | `./claude-flow sparc run code "implement auth"`|
| `tdd` | Testes | `./claude-flow sparc run tdd "create test suite"`|
| `security-review` | Auditoria | `./claude-flow sparc run security-review "audit code"`|
| `integration` | Integração | `./claude-flow sparc run integration "connect services"`|
| `devops` | Deploy/CI-CD | `./claude-flow sparc run devops "setup pipeline"`|

### Comandos SPARC Essenciais:
```bash
# Listar todos os modos disponíveis
./claude-flow sparc modes

# Executar comando SPARC simples
./claude-flow sparc "build a REST API"

# Executar modo específico
./claude-flow sparc run coder "implement user authentication"

# Ver ajuda do SPARC
./claude-flow sparc --help
```

---

## 🔥 Fluxos de Trabalho Práticos

### 1. Desenvolvimento Full-Stack Automatizado
```bash
# Pesquisa e planejamento
./claude-flow sparc run ask "research best practices for microservices"

# Design da arquitetura
./claude-flow sparc run architect "design scalable user authentication system"

# Implementação
./claude-flow sparc run code "implement user service with JWT"

# Testes
./claude-flow sparc run tdd "create comprehensive test suite for auth"

# Integração
./claude-flow sparc run integration "integrate auth with main app"

# Deploy
./claude-flow sparc run devops "setup CI/CD pipeline with Docker"
```

### 2. Desenvolvimento Paralelo com Swarm
```bash
# Deploy de múltiplos agentes trabalhando simultaneamente
./claude-flow swarm "Build e-commerce platform with user auth, payment, and admin panel" \
--strategy development \
--max-agents 5 \
--parallel \
--monitor
```

### 3. Desenvolvimento com BatchTool (Paralelo)
```bash
# Executar múltiplas tarefas simultaneamente
batchtool run --parallel \
"./claude-flow sparc run architect 'design user authentication'" \
"./claude-flow sparc run code 'implement login API'" \
"./claude-flow sparc run tdd 'create auth tests'" \
"./claude-flow sparc run security-review 'audit auth flow'"
```

---

## 💾 Sistema de Memória

### Armazenar Informações:
```bash
# Salvar requisitos do projeto
./claude-flow memory store requirements "User auth with JWT and 2FA"

# Salvar decisões de arquitetura
./claude-flow memory store architecture "Microservice design with Docker"

# Salvar configurações importantes
./claude-flow memory store config "Database: PostgreSQL, Cache: Redis"
```

### Consultar Informações:
```bash
# Buscar por tópico
./claude-flow memory query auth

# Buscar por palavra-chave
./claude-flow memory query database

# Ver toda a memória
./claude-flow memory list
```

---

## 📊 Monitoramento e Gestão

### Comandos de Status:
```bash
# Status geral do sistema
./claude-flow status

# Status do servidor MCP
./claude-flow mcp status

# Monitoramento em tempo real
./claude-flow monitor --dashboard
```

### Gestão de Tarefas:
```bash
# Criar tarefa
./claude-flow task create research "Market analysis for AI tools"

# Executar workflow
./claude-flow task workflow examples/development-pipeline.json
```

---

## 🚨 Troubleshooting

### Problemas Comuns:

#### 1. Claude Code não reconhece o comando
```bash
# Verifique se o wrapper foi criado
ls -la claude-flow

# Se não existir, rode novamente:
npx claude-flow@latest init --sparc
```

#### 2. Erro de permissões
```bash
# Dar permissão de execução (Linux/Mac)
chmod +x claude-flow

# Windows: executar como administrador
```

#### 3. Porta já em uso
```bash
# Usar porta diferente
./claude-flow start --ui --port 3001
```

#### 4. Node.js/npm desatualizados
```bash
# Atualizar Node.js para versão 18+
# Baixar em: https://nodejs.org/

# Atualizar npm
npm install -g npm@latest
```

---

## 📚 Exemplos de Uso Completos

### Exemplo 1: API REST Completa
```bash
# 1. Inicializar projeto
npx claude-flow@latest init --sparc

# 2. Planejar arquitetura
./claude-flow sparc run architect "design REST API for task management with authentication"

# 3. Implementar
./claude-flow sparc run code "implement task CRUD API with Express.js"

# 4. Criar testes
./claude-flow sparc run tdd "create unit and integration tests"

# 5. Configurar deploy
./claude-flow sparc run devops "setup Docker and deployment pipeline"
```

### Exemplo 2: Aplicação React + Backend
```bash
# Desenvolvimento paralelo frontend/backend
./claude-flow swarm "Build React todo app with Node.js backend and PostgreSQL" \
--strategy full-stack \
--max-agents 3 \
--parallel
```

---

## 🎯 Dicas de Produtividade

### 1. Use a Interface Web
```bash
# Sempre inicie com UI para monitoramento visual
./claude-flow start --ui --port 3000
```

### 2. Combine Modos para Fluxos Complexos
```bash
# Sequência otimizada para novos projetos
./claude-flow sparc run ask "research [seu tópico]"
./claude-flow sparc run architect "design [sua arquitetura]"
./claude-flow sparc run code "implement [sua funcionalidade]"
./claude-flow sparc run tdd "test [seu código]"
```

### 3. Use Memória Ativamente
```bash
# Sempre documente decisões importantes
./claude-flow memory store "decision-$(date +%Y%m%d)" "Escolhemos PostgreSQL por [razões]"
```

### 4. Monitore Recursos
```bash
# Verificar status regularmente
./claude-flow status

# Usar dashboard para projetos complexos
./claude-flow monitor --dashboard
```

---

## 📖 Comandos de Referência Rápida

### Essenciais:
```bash
npx claude-flow@latest init --sparc    # Instalação inicial
./claude-flow start --ui --port 3000   # Iniciar com UI
./claude-flow sparc modes              # Ver modos disponíveis
./claude-flow status                   # Status do sistema
```

### Desenvolvimento:
```bash
./claude-flow sparc "seu comando"      # Comando simples
./claude-flow sparc run code "tarefa"  # Modo específico
./claude-flow swarm "projeto complexo" # Multi-agentes
```

### Memória:
```bash
./claude-flow memory store key "value" # Armazenar
./claude-flow memory query termo       # Buscar
./claude-flow memory list              # Listar tudo
```

---

## 🎉 Próximos Passos

1. **Instale**: Execute `npx claude-flow@latest init --sparc`
2. **Inicie**: Execute `./claude-flow start --ui --port 3000`
3. **Teste**: Execute `./claude-flow sparc "create a simple hello world API"`
4. **Explore**: Teste diferentes modos e fluxos de trabalho
5. **Automatize**: Use swarms para projetos complexos

## 📞 Suporte

- **GitHub**: https://github.com/ruvnet/claude-flow
- **Issues**: Para reportar bugs ou solicitar features
- **Documentação**: README.md no repositório oficial

---

**Sucesso!** 🎯 Agora você tem tudo que precisa para usar Claude-Flow efetivamente. Comece com comandos simples e evolua para fluxos mais complexos conforme ganha experiência.