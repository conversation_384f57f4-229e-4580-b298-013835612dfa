// FORÇA POSTGRESQL EM TODAS AS MENSAGENS - SOLUÇÃO RADICAL
const fs = require('fs');

console.log('🔥 FORÇANDO POSTGRESQL EM TODAS AS MENSAGENS');
console.log('============================================');
console.log('Esta é a solução RADICAL - PostgreSQL será ativado SEMPRE!');

const deepSeekPath = './backend/src/services/deepSeekService.ts';

if (fs.existsSync(deepSeekPath)) {
  let content = fs.readFileSync(deepSeekPath, 'utf8');
  
  // Forçar isProtocolQuery = true sempre
  const oldDetection = `      const isProtocolQuery = messageQuery.includes('protocolo') || 
                             messageQuery.includes('processo') ||
                             messageQuery.includes('alvará') ||
                             messageQuery.includes('licença') ||
                             messageQuery.includes('licenca') ||
                             messageQuery.includes('andamento') ||
                             messageQuery.includes('situação') ||
                             messageQuery.includes('situacao') ||
                             messageQuery.includes('quantos') ||
                             messageQuery.includes('quantidade') ||
                             messageQuery.includes('número') ||
                             messageQuery.includes('numero') ||
                             messageQuery.includes('estatística') ||
                             messageQuery.includes('estatistica') ||
                             messageQuery.includes('dados') ||
                             messageQuery.includes('total') ||
                             messageQuery.includes('registrados') ||
                             messageQuery.includes('existem') ||
                             messageQuery.includes('temos') ||
                             messageQuery.includes('municipal') ||
                             messageQuery.includes('municipais');`;

  const newDetection = `      // FORÇAR POSTGRESQL SEMPRE - SOLUÇÃO RADICAL
      const isProtocolQuery = true; // SEMPRE ATIVA POSTGRESQL
      
      console.log('🔥 POSTGRESQL FORÇADO PARA TODAS AS MENSAGENS!');
      
      // Detecção original (mantida para referência):
      const originalDetection = messageQuery.includes('protocolo') || 
                             messageQuery.includes('processo') ||
                             messageQuery.includes('alvará') ||
                             messageQuery.includes('licença') ||
                             messageQuery.includes('licenca') ||
                             messageQuery.includes('andamento') ||
                             messageQuery.includes('situação') ||
                             messageQuery.includes('situacao') ||
                             messageQuery.includes('quantos') ||
                             messageQuery.includes('quantidade') ||
                             messageQuery.includes('número') ||
                             messageQuery.includes('numero') ||
                             messageQuery.includes('estatística') ||
                             messageQuery.includes('estatistica') ||
                             messageQuery.includes('dados') ||
                             messageQuery.includes('total') ||
                             messageQuery.includes('registrados') ||
                             messageQuery.includes('existem') ||
                             messageQuery.includes('temos') ||
                             messageQuery.includes('municipal') ||
                             messageQuery.includes('municipais');`;
  
  if (content.includes('const isProtocolQuery = messageQuery.includes')) {
    content = content.replace(oldDetection, newDetection);
    fs.writeFileSync(deepSeekPath, content);
    
    console.log('✅ POSTGRESQL FORÇADO EM TODAS AS MENSAGENS!');
    console.log('🚨 ATENÇÃO: Agora TODA mensagem vai buscar PostgreSQL!');
    console.log('');
    console.log('🎯 TESTE FINAL:');
    console.log('===============');
    console.log('1. Aguarde tsx recompilar (3-5 segundos)');
    console.log('2. Faça QUALQUER pergunta');
    console.log('3. Deve aparecer dados PostgreSQL na resposta');
    console.log('');
    console.log('📝 Exemplo de teste:');
    console.log('curl -X POST http://localhost:3001/api/chat/message \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"message":"Olá","secretaria":"administracao","userId":"test"}\'');
    
  } else {
    console.log('❌ Não foi possível encontrar o padrão de detecção');
    console.log('💡 Aplicar correção manualmente');
  }
} else {
  console.log('❌ Arquivo deepSeekService.ts não encontrado');
}

console.log('\n🏆 ESTA É A GARANTIA ABSOLUTA!');
console.log('Se PostgreSQL não funcionar agora, o problema está em outro lugar.');