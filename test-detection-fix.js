// Teste após corrigir detecção de intenção
const http = require('http');

console.log('🔧 TESTE APÓS CORREÇÃO DA DETECÇÃO DE INTENÇÃO');
console.log('===============================================');
console.log('✅ Adicionadas palavras-chave: quantos, dados, municipal, etc.');
console.log('');

async function testDetectionFix() {
  console.log('⏳ Aguardando 3 segundos para tsx recompilar...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  const timestamp = Date.now();
  const testMessage = `Quantos protocolos municipais temos hoje ${timestamp}?`;
  
  console.log('📝 Pergunta de teste:', testMessage);
  console.log('🎯 Esta pergunta DEVE ativar PostgreSQL agora (contém: quantos + municipais)');
  console.log('🚀 Enviando requisição...');
  
  try {
    const response = await makeRequest('http://localhost:3001/api/chat/message', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    }, {
      message: testMessage,
      secretaria: "administracao", 
      userId: "test-detection-" + timestamp
    });
    
    console.log('\n📊 RESULTADO:');
    console.log('=============');
    console.log('Status HTTP:', response.status);
    
    if (response.data.success) {
      const chatResponse = response.data.data.response;
      const cacheHit = response.data.cacheHit;
      
      console.log('Cache Hit:', cacheHit);
      console.log('Tempo:', response.data.metadata?.processingTime || 'N/A', 'ms');
      
      console.log('\n🤖 RESPOSTA APÓS CORREÇÃO:');
      console.log('='.repeat(80));
      console.log(chatResponse);
      console.log('='.repeat(80));
      
      // Análise crítica
      if (chatResponse.includes('26.760') || chatResponse.includes('26760') ||
          chatResponse.includes('13.697') || chatResponse.includes('13697') ||
          chatResponse.includes('protocolos no sistema') ||
          chatResponse.includes('consultando nosso sistema') ||
          chatResponse.includes('dados atualizados') ||
          chatResponse.includes('tempo real')) {
        console.log('\n🎊 SUCESSO TOTAL! DETECÇÃO CORRIGIDA!');
        console.log('=====================================');
        console.log('✅ Integração PostgreSQL 100% funcionando');
        console.log('✅ Detecção de intenção corrigida');
        console.log('✅ Dados reais sendo exibidos');
        console.log('✅ Fim das respostas genéricas');
        console.log('');
        console.log('🏆 MISSÃO DEFINITIVAMENTE CUMPRIDA!');
        
      } else if (chatResponse.includes('não disponibiliza publicamente') ||
                 chatResponse.includes('não tenho acesso')) {
        console.log('\n❌ AINDA NÃO FUNCIONOU');
        console.log('======================');
        console.log('🔧 Possíveis problemas restantes:');
        console.log('   - Detecção ainda não captura esta pergunta');
        console.log('   - Erro nas queries PostgreSQL');
        console.log('   - Problema na construção do prompt');
        console.log('💡 Verificar logs do backend para mais detalhes');
        
      } else {
        console.log('\n⚠️ RESULTADO INDETERMINADO');
        console.log('===========================');
        console.log('🔍 Resposta não contém padrões conhecidos');
        console.log('💡 Analisar manualmente se contém dados municipais');
      }
      
    } else {
      console.log('\n❌ ERRO NA API:', response.data.error);
    }
    
  } catch (error) {
    console.error('\n❌ ERRO:', error.message);
  }
  
  console.log('\n🎯 PRÓXIMA AÇÃO:');
  console.log('================');
  console.log('Se ainda não funcionou:');
  console.log('1. Verificar logs do backend para "🔍 Buscando dados PostgreSQL"');
  console.log('2. Se não aparecer, expandir mais a detecção');
  console.log('3. Se aparecer mas falhar, problema nas queries');
}

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const reqOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.pathname,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 25000
    };
    
    const req = http.request(reqOptions, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsedBody });
        } catch {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

testDetectionFix();