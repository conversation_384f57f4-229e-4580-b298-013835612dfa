import { useState, useRef, KeyboardEvent } from 'react';
import { Button } from '@/components/ui';
import { clsx } from 'clsx';

interface MessageInputProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  placeholder?: string;
}

export function MessageInput({ onSendMessage, isLoading = false, placeholder = 'Digite sua mensagem...' }: MessageInputProps) {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !isLoading) {
      onSendMessage(trimmedMessage);
      setMessage('');
      
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  // Auto-resize textarea
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    
    // Auto-resize
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
  };

  return (
    <div className="border-t border-pv-gray-200 bg-white p-4">
      <div className="flex items-end space-x-3">
        {/* File Upload Button */}
        <button
          type="button"
          className="flex-shrink-0 p-2 text-pv-gray-500 hover:text-pv-blue-primary hover:bg-pv-blue-50 rounded-lg transition-colors"
          title="Anexar arquivo"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
          </svg>
        </button>

        {/* Message Input */}
        <div className="flex-1">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            rows={1}
            disabled={isLoading}
            className={clsx(
              'w-full resize-none rounded-lg border border-pv-gray-300 px-4 py-3',
              'focus:border-pv-blue-primary focus:ring-2 focus:ring-pv-blue-500 focus:ring-offset-2',
              'placeholder:text-pv-gray-500 text-pv-gray-900',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              'transition-colors duration-200'
            )}
            style={{ minHeight: '44px', maxHeight: '120px' }}
          />
        </div>

        {/* Send Button */}
        <Button
          onClick={handleSubmit}
          disabled={!message.trim() || isLoading}
          isLoading={isLoading}
          size="md"
          className="flex-shrink-0"
        >
          {isLoading ? (
            'Enviando...'
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          )}
        </Button>
      </div>

      {/* Helper text */}
      <div className="mt-2 text-xs text-pv-gray-500">
        Pressione <kbd className="px-1 py-0.5 bg-pv-gray-100 rounded">Enter</kbd> para enviar, 
        <kbd className="px-1 py-0.5 bg-pv-gray-100 rounded ml-1">Shift + Enter</kbd> para nova linha
      </div>
    </div>
  );
}