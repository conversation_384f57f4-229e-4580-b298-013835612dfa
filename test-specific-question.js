// Teste com pergunta mais específica para ativar PostgreSQL
const http = require('http');

console.log('🎯 TESTE COM PERGUNTA ESPECÍFICA PARA ATIVAR POSTGRESQL');
console.log('====================================================');

async function testSpecificQuestion() {
  const testQuestions = [
    "Quantos protocolos municipais temos registrados?",
    "Quantos protocolos em andamento existem hoje?", 
    "Quais são os protocolos recentes de alvará?",
    "Mostre estatísticas dos protocolos municipais"
  ];
  
  for (let i = 0; i < testQuestions.length; i++) {
    const question = testQuestions[i];
    const timestamp = Date.now() + i;
    
    console.log(`\n${i + 1}. 🧪 TESTANDO: "${question}"`);
    console.log('='.repeat(60));
    
    try {
      const response = await makeRequest('http://localhost:3001/api/chat/message', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      }, {
        message: question,
        secretaria: "administracao", 
        userId: "test-specific-" + timestamp
      });
      
      if (response.data.success) {
        const chatResponse = response.data.data.response;
        const cacheHit = response.data.cacheHit;
        
        console.log('Cache Hit:', cacheHit);
        console.log('Tempo:', response.data.metadata?.processingTime || 'N/A', 'ms');
        
        console.log('\n🤖 RESPOSTA:');
        console.log('-'.repeat(40));
        console.log(chatResponse.substring(0, 300) + '...');
        console.log('-'.repeat(40));
        
        // Verificar se contém dados PostgreSQL
        if (chatResponse.includes('26.760') || chatResponse.includes('26760') ||
            chatResponse.includes('13.697') || chatResponse.includes('13697') ||
            chatResponse.includes('protocolos no sistema') ||
            chatResponse.includes('consultando nosso sistema') ||
            chatResponse.includes('dados atualizados')) {
          console.log('✅ SUCESSO! Esta pergunta ativou a integração PostgreSQL!');
          console.log('🎊 Dados reais encontrados na resposta!');
          
          // Mostrar resposta completa se funcionou
          console.log('\n🏆 RESPOSTA COMPLETA COM DADOS REAIS:');
          console.log('='.repeat(80));
          console.log(chatResponse);
          console.log('='.repeat(80));
          break;
          
        } else if (chatResponse.includes('não tenho acesso')) {
          console.log('❌ Ainda responde sem acesso ao banco');
        } else {
          console.log('⚠️ Resposta genérica - integração pode não ter sido ativada');
        }
        
      } else {
        console.log('❌ Erro na API:', response.data.error);
      }
      
      // Pausa entre testes
      if (i < testQuestions.length - 1) {
        console.log('\n⏳ Aguardando 2 segundos...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
    } catch (error) {
      console.log('❌ Erro:', error.message);
    }
  }
  
  console.log('\n🎯 CONCLUSÃO DO TESTE:');
  console.log('======================');
  console.log('Se nenhuma pergunta ativou a integração PostgreSQL:');
  console.log('1. Problema na detecção de intenção (palavras-chave)');
  console.log('2. Problema na execução das queries PostgreSQL');
  console.log('3. Problema na construção do prompt com dados');
  console.log('');
  console.log('Se alguma pergunta funcionou:');
  console.log('✅ Integração está funcionando!');
  console.log('✅ Ajustar detecção para mais tipos de pergunta');
}

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const reqOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.pathname,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 25000
    };
    
    const req = http.request(reqOptions, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsedBody });
        } catch {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

testSpecificQuestion();