import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { clsx } from 'clsx';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  variant?: 'cost' | 'performance' | 'cache' | 'savings';
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export function MetricCard({ 
  title, 
  value, 
  subtitle, 
  icon, 
  variant = 'cache',
  trend 
}: MetricCardProps) {
  const iconColors = {
    cost: 'text-pv-yellow-600',
    performance: 'text-pv-cyan-600', 
    cache: 'text-pv-blue-600',
    savings: 'text-pv-orange-600'
  };

  return (
    <Card variant={variant} className="relative overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          {icon && (
            <div className={clsx('p-2 rounded-lg bg-white/50', iconColors[variant])}>
              {icon}
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2">
          <div className="text-2xl font-bold text-pv-gray-800">
            {typeof value === 'number' ? value.toLocaleString('pt-BR') : value}
          </div>
          
          {subtitle && (
            <p className="text-sm text-pv-gray-600">{subtitle}</p>
          )}
          
          {trend && (
            <div className="flex items-center space-x-1">
              <svg 
                className={clsx(
                  'w-4 h-4',
                  trend.isPositive ? 'text-green-600' : 'text-red-600'
                )} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d={trend.isPositive ? 'M7 14l9-9 9 9' : 'M17 10l-9 9-9-9'} 
                />
              </svg>
              <span className={clsx(
                'text-sm font-medium',
                trend.isPositive ? 'text-green-600' : 'text-red-600'
              )}>
                {Math.abs(trend.value)}%
              </span>
              <span className="text-sm text-pv-gray-500">vs ontem</span>
            </div>
          )}
        </div>
        
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-20 h-20 opacity-10">
          <div className={clsx(
            'w-full h-full rounded-full',
            variant === 'cost' && 'bg-pv-yellow-500',
            variant === 'performance' && 'bg-pv-cyan-500',
            variant === 'cache' && 'bg-pv-blue-500',
            variant === 'savings' && 'bg-pv-orange-500'
          )} />
        </div>
      </CardContent>
    </Card>
  );
}