// Teste para Windows - Execute no PowerShell: node test-windows.js
const http = require('http');

console.log('🧪 Testando integração PostgreSQL (Windows)...');

// Testar endpoint PostgreSQL
const postData = JSON.stringify({
  message: "Quantos protocolos de alvará temos em andamento?",
  userId: "test-user",
  secretaria: "administracao"
});

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/chat/message',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('\n📤 Enviando pergunta: "Quantos protocolos de alvará temos em andamento?"');
console.log('⏳ Aguardando resposta...\n');

const req = http.request(options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      
      if (response.success) {
        console.log('✅ Chat respondeu com sucesso!');
        console.log('\n📱 RESPOSTA DO CHATBOT:');
        console.log('═'.repeat(80));
        console.log(response.data.content);
        console.log('═'.repeat(80));
        
        // Análise da resposta
        const content = response.data.content.toLowerCase();
        const hasSpecificNumbers = /\d{1,3}(?:,\d{3})*|\d{2,}/.test(response.data.content);
        const hasProtocolNumbers = /20\d{10}/.test(response.data.content);
        const hasNoAccessMessage = content.includes('não tenho acesso') || 
                                   content.includes('no momento, não tenho') ||
                                   content.includes('infelizmente, não tenho');
        
        console.log('\n🔍 ANÁLISE DA INTEGRAÇÃO:');
        console.log('─'.repeat(40));
        
        if (hasSpecificNumbers) {
          console.log('✅ Contém números específicos (bom sinal!)');
        } else {
          console.log('❌ Não contém números específicos');
        }
        
        if (hasProtocolNumbers) {
          console.log('✅ Contém números de protocolo reais (EXCELENTE!)');
        } else {
          console.log('⚠️ Não contém números de protocolo');
        }
        
        if (hasNoAccessMessage) {
          console.log('❌ PROBLEMA: Ainda contém "não tenho acesso"');
          console.log('   → A integração PostgreSQL NÃO está funcionando!');
        } else {
          console.log('✅ SUCESSO: Não contém "não tenho acesso"');
          console.log('   → A integração PostgreSQL ESTÁ FUNCIONANDO! 🎉');
        }
        
        console.log('\n💰 Informações adicionais:');
        console.log(`   Custo: $${response.data.cost?.total?.toFixed(4) || '0.0000'}`);
        console.log(`   Tokens: ${response.data.tokens?.total || 0}`);
        console.log(`   Modelo: ${response.data.model || 'N/A'}`);
        
        // Diagnóstico final
        console.log('\n🎯 DIAGNÓSTICO FINAL:');
        console.log('═'.repeat(50));
        
        if (hasProtocolNumbers && !hasNoAccessMessage) {
          console.log('🎊 INTEGRAÇÃO POSTGRESQL: FUNCIONANDO PERFEITAMENTE!');
          console.log('   O chatbot agora tem acesso aos dados reais!');
        } else if (hasSpecificNumbers && !hasNoAccessMessage) {
          console.log('🟡 INTEGRAÇÃO POSTGRESQL: PARCIALMENTE FUNCIONANDO');
          console.log('   Tem números mas pode estar usando dados genéricos');
        } else {
          console.log('🔴 INTEGRAÇÃO POSTGRESQL: NÃO FUNCIONANDO');
          console.log('   Ainda está usando respostas padrão');
          console.log('\n🔧 POSSÍVEIS SOLUÇÕES:');
          console.log('   1. Verificar se o arquivo deepSeekService.ts foi modificado');
          console.log('   2. Verificar logs do servidor backend');
          console.log('   3. Testar conexão PostgreSQL manualmente');
        }
        
      } else {
        console.log('❌ Erro na resposta:', response.error);
      }
      
    } catch (error) {
      console.log('❌ Erro ao parsear resposta:', error.message);
      console.log('Resposta bruta:', data);
    }
  });
});

req.on('error', (error) => {
  console.log('❌ Erro de conexão:', error.message);
  console.log('\n🔧 Verifique se:');
  console.log('   1. O servidor backend está rodando na porta 3001');
  console.log('   2. Não há firewall bloqueando a conexão');
  console.log('   3. Execute este teste no Windows (não no WSL)');
});

req.write(postData);
req.end();

// Timeout de segurança
setTimeout(() => {
  console.log('\n⏰ Timeout: O teste demorou mais de 30 segundos');
  process.exit(1);
}, 30000);