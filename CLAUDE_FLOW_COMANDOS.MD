# 📚 Claude-Flow - Referência Completa de Comandos

## 📋 Índice
- [Instalação e Configuração](#-instalação-e-configuração)
- [Sistema e Status](#-sistema-e-status)
- [Gerenciamento de Agentes](#-gerenciamento-de-agentes)
- [Modos SPARC](#-modos-sparc)
- [Operações Swarm](#-operações-swarm)
- [Gerenciamento de Memória](#-gerenciamento-de-memória)
- [Gerenciamento de Tarefas](#-gerenciamento-de-tarefas)
- [Monitoramento](#-monitoramento)
- [Servidor MCP](#-servidor-mcp)
- [Gerenciamento de Projetos](#-gerenciamento-de-projetos)
- [Deploy e Cloud](#-deploy-e-cloud)
- [Segurança](#-segurança)
- [Analytics](#-analytics)
- [BatchTool](#-batchtool)
- [Solução de Problemas](#-solução-de-problemas)

---

## 🚀 Instalação e Configuração

### Instalação via NPX (Recomendada)
```bash
# Instalar e inicializar com ambiente SPARC
npx claude-flow@latest init --sparc

# Inicializar com opções específicas
npx claude-flow@latest init --sparc --port 3000
```

### Instalação Global
```bash
# Instalar globalmente
npm install -g claude-flow

# Inicializar em qualquer lugar
claude-flow init --sparc

# Usar diretamente
claude-flow start --ui
```

### Instalação Local
```bash
# Adicionar ao projeto
npm install claude-flow --save-dev

# Inicializar
npx claude-flow init --sparc

# Usar com wrapper local
./claude-flow start --ui
```

### Opções de Inicialização
```bash
# Inicialização básica
./claude-flow init

# Inicializar com ambiente SPARC
./claude-flow init --sparc

# Inicializar com porta customizada
./claude-flow init --sparc --port 4000

# Inicializar com configurações específicas
./claude-flow init --sparc --timeout 600 --agents 10
```

---

## 🔧 Sistema e Status

### Gerenciamento do Sistema
```bash
# Verificar status do sistema
./claude-flow status

# Iniciar sistema de orquestração
./claude-flow start

# Iniciar com Interface Web
./claude-flow start --ui

# Iniciar com porta customizada
./claude-flow start --ui --port 3000

# Parar o sistema
./claude-flow stop

# Reiniciar o sistema
./claude-flow restart

# Verificar versão
./claude-flow version

# Mostrar ajuda
./claude-flow help
./claude-flow --help
```

### Configuração
```bash
# Mostrar configuração atual
./claude-flow config show

# Atualizar configuração
./claude-flow config set timeout 600
./claude-flow config set max-agents 10

# Resetar configuração para padrões
./claude-flow config reset

# Validar configuração
./claude-flow config validate
```

---

## 🤖 Gerenciamento de Agentes

### Operações com Agentes
```bash
# Criar um novo agente
./claude-flow agent spawn researcher --name "DataBot"
./claude-flow agent spawn coder --name "DevBot"
./claude-flow agent spawn architect --name "ArchBot"

# Listar todos os agentes
./claude-flow agent list

# Obter informações do agente
./claude-flow agent info agent-123
./claude-flow agent info DataBot

# Terminar agente específico
./claude-flow agent terminate agent-123
./claude-flow agent terminate DataBot

# Terminar todos os agentes
./claude-flow agent terminate-all

# Status do agente
./claude-flow agent status agent-123
./claude-flow agent status --all
```

### Comunicação com Agentes
```bash
# Enviar mensagem para agente
./claude-flow agent message agent-123 "analyze this code"

# Transmitir para todos os agentes
./claude-flow agent broadcast "new requirements updated"

# Logs do agente
./claude-flow agent logs agent-123
./claude-flow agent logs --all --tail 100
```

---

## ⭐ Modos SPARC

### Comandos SPARC Principais
```bash
# Listar todos os modos SPARC disponíveis
./claude-flow sparc modes

# Executar comando SPARC simples
./claude-flow sparc "build a REST API"

# Mostrar ajuda do SPARC
./claude-flow sparc help
./claude-flow sparc --help
```

### Modos Especializados

#### Arquitetura e Design
```bash
# Design de arquitetura de sistema
./claude-flow sparc run architect "design scalable microservice architecture"
./claude-flow sparc run architect "create database schema for e-commerce"
./claude-flow sparc run architect "design API for mobile app"

# Modo ask para pesquisa e análise
./claude-flow sparc run ask "research best practices for microservices"
./claude-flow sparc run ask "analyze current market trends in AI"
./claude-flow sparc run ask "compare React vs Vue.js for this project"
```

#### Desenvolvimento
```bash
# Implementação de código
./claude-flow sparc run code "implement user authentication with JWT"
./claude-flow sparc run code "create REST API for user management"
./claude-flow sparc run code "build responsive landing page"

# Modo coder (apelido para code)
./claude-flow sparc run coder "implement payment processing"
./claude-flow sparc run coder "create admin dashboard"
```

#### Testes
```bash
# Desenvolvimento orientado a testes
./claude-flow sparc run tdd "create test suite for user authentication"
./claude-flow sparc run tdd "implement unit tests for API endpoints"
./claude-flow sparc run tdd "create integration tests for payment system"

# Garantia de qualidade
./claude-flow sparc run qa "review code quality and suggest improvements"
./claude-flow sparc run qa "perform usability testing on frontend"
```

#### Segurança
```bash
# Revisão e auditoria de segurança
./claude-flow sparc run security-review "audit authentication system"
./claude-flow sparc run security-review "analyze API for vulnerabilities"
./claude-flow sparc run security-review "review data encryption methods"

# Implementação de segurança
./claude-flow sparc run security "implement OAuth2 authentication"
./claude-flow sparc run security "add input validation and sanitization"
```

#### DevOps e Deploy
```bash
# Operações DevOps
./claude-flow sparc run devops "setup CI/CD pipeline with GitHub Actions"
./claude-flow sparc run devops "create Docker containers for microservices"
./claude-flow sparc run devops "configure Kubernetes deployment"

# Deploy
./claude-flow sparc run deploy "deploy application to AWS"
./claude-flow sparc run deploy "setup production environment"
```

#### Integração e Sistema
```bash
# Integração de sistemas
./claude-flow sparc run integration "integrate payment gateway with API"
./claude-flow sparc run integration "connect frontend with backend services"
./claude-flow sparc run integration "setup third-party API integrations"

# Otimização de performance
./claude-flow sparc run performance "optimize database queries"
./claude-flow sparc run performance "improve API response times"

# Documentação
./claude-flow sparc run docs "create API documentation"
./claude-flow sparc run docs "write user manual for application"
```

### Todos os Modos SPARC Disponíveis
```bash
# Lista completa de modos
./claude-flow sparc run architect     # Arquitetura e design de sistema
./claude-flow sparc run ask          # Pesquisa e análise
./claude-flow sparc run code         # Implementação de código
./claude-flow sparc run coder        # Desenvolvimento de código (apelido)
./claude-flow sparc run tdd          # Desenvolvimento orientado a testes
./claude-flow sparc run qa           # Garantia de qualidade
./claude-flow sparc run security-review  # Auditoria de segurança
./claude-flow sparc run security     # Implementação de segurança
./claude-flow sparc run devops       # Operações DevOps
./claude-flow sparc run deploy       # Deploy
./claude-flow sparc run integration  # Integração de sistemas
./claude-flow sparc run performance  # Otimização de performance
./claude-flow sparc run docs         # Documentação
./claude-flow sparc run research     # Pesquisa e investigação
./claude-flow sparc run design       # Design UI/UX
./claude-flow sparc run data         # Análise de dados
./claude-flow sparc run ml           # Machine learning
```

---

## 🐝 Operações Swarm

### Comandos Swarm Básicos
```bash
# Deploy de swarm básico
./claude-flow swarm "Build e-commerce platform"

# Swarm com estratégia específica
./claude-flow swarm "Create mobile app backend" --strategy development

# Swarm com máximo de agentes
./claude-flow swarm "Build social media platform" --max-agents 5

# Execução paralela de swarm
./claude-flow swarm "Develop full-stack application" --parallel

# Swarm com monitoramento
./claude-flow swarm "Create microservices architecture" --monitor
```

### Operações Swarm Avançadas
```bash
# Deploy completo de swarm
./claude-flow swarm "Build e-commerce platform with user auth, payment, and admin panel" \
--strategy development \
--max-agents 5 \
--parallel \
--monitor

# Swarm com papéis específicos
./claude-flow swarm "Develop API and frontend" \
--agents "architect,coder,tdd,security" \
--strategy full-stack

# Swarm com limites de tempo
./claude-flow swarm "Quick prototype development" \
--timeout 3600 \
--fast-mode

# Status e gerenciamento de swarm
./claude-flow swarm status
./claude-flow swarm list
./claude-flow swarm terminate swarm-id-123
```

### Estratégias de Swarm
```bash
# Estratégias disponíveis
--strategy development    # Ciclo completo de desenvolvimento
--strategy prototyping   # Prototipagem rápida
--strategy migration     # Migração/refatoração de código
--strategy full-stack    # Frontend + Backend
--strategy testing       # Testes abrangentes
--strategy security      # Desenvolvimento focado em segurança
--strategy performance   # Otimização de performance
```

---

## 🧠 Gerenciamento de Memória

### Armazenar Informações
```bash
# Armazenar requisitos do projeto
./claude-flow memory store requirements "User auth with JWT and 2FA support"

# Armazenar decisões de arquitetura
./claude-flow memory store architecture "Microservice design with Docker containers"

# Armazenar detalhes de configuração
./claude-flow memory store config "Database: PostgreSQL, Cache: Redis, Queue: RabbitMQ"

# Armazenar com categorias
./claude-flow memory store database "PostgreSQL 14 with connection pooling"
./claude-flow memory store security "OAuth2 + JWT with refresh tokens"
./claude-flow memory store deployment "AWS ECS with ALB and RDS"
```

### Recuperar Informações
```bash
# Consultar por palavra-chave
./claude-flow memory query auth
./claude-flow memory query database
./claude-flow memory query deployment

# Buscar com múltiplas palavras-chave
./claude-flow memory query "user authentication JWT"

# Buscar por categoria
./claude-flow memory query --category security
./claude-flow memory query --category database
```

### Gerenciamento de Memória
```bash
# Listar todas as memórias armazenadas
./claude-flow memory list

# Listar por categoria
./claude-flow memory list --category requirements
./claude-flow memory list --category architecture

# Mostrar detalhes da memória
./claude-flow memory show requirements
./claude-flow memory show architecture

# Atualizar memória existente
./claude-flow memory update requirements "Updated user auth requirements with OAuth2"

# Deletar memória
./claude-flow memory delete old-config
./claude-flow memory delete --category outdated

# Limpar toda a memória
./claude-flow memory clear

# Exportar memória
./claude-flow memory export memory-backup.json

# Importar memória
./claude-flow memory import memory-backup.json
```

---

## 📋 Gerenciamento de Tarefas

### Operações com Tarefas
```bash
# Criar nova tarefa
./claude-flow task create research "Market analysis for AI tools"
./claude-flow task create development "Implement user authentication"
./claude-flow task create testing "Create comprehensive test suite"

# Listar todas as tarefas
./claude-flow task list

# Mostrar detalhes da tarefa
./claude-flow task show task-id-123

# Atualizar tarefa
./claude-flow task update task-id-123 --status in-progress
./claude-flow task update task-id-123 --priority high

# Completar tarefa
./claude-flow task complete task-id-123

# Deletar tarefa
./claude-flow task delete task-id-123
```

### Workflows de Tarefas
```bash
# Executar workflow de arquivo
./claude-flow task workflow examples/development-pipeline.json
./claude-flow task workflow workflows/testing-suite.yaml

# Criar workflow
./claude-flow task workflow create "API Development" \
--steps "architect,code,tdd,security-review,deploy"

# Listar workflows disponíveis
./claude-flow task workflow list

# Mostrar detalhes do workflow
./claude-flow task workflow show "API Development"
```

### Agendamento de Tarefas
```bash
# Agendar tarefa
./claude-flow task schedule "Daily backup" --cron "0 2 * * *"
./claude-flow task schedule "Weekly report" --interval weekly

# Listar tarefas agendadas
./claude-flow task schedule list

# Cancelar tarefa agendada
./claude-flow task schedule cancel schedule-id-123
```

---

## 📊 Monitoramento

### Comandos de Monitoramento
```bash
# Monitoramento em tempo real
./claude-flow monitor

# Dashboard de monitoramento
./claude-flow monitor --dashboard

# Monitoramento com intervalo customizado
./claude-flow monitor --interval 5s

# Monitoramento de recursos
./claude-flow monitor --resources

# Monitoramento de performance
./claude-flow monitor --performance
```

### Métricas e Analytics
```bash
# Mostrar métricas do sistema
./claude-flow metrics

# Métricas de agentes
./claude-flow metrics agents

# Métricas de tasks
./claude-flow metrics tasks

# Métricas de performance
./claude-flow metrics performance

# Exportar métricas
./claude-flow metrics export metrics-$(date +%Y%m%d).json
```

### Logs e Debugging
```bash
# Ver logs do sistema
./claude-flow logs

# Logs com nível específico
./claude-flow logs --level debug
./claude-flow logs --level error

# Logs de componente específico
./claude-flow logs --component agent
./claude-flow logs --component swarm

# Seguir logs em tempo real
./claude-flow logs --follow

# Logs com filtro
./claude-flow logs --filter "error"
./claude-flow logs --since "1h"
```

---

## 🔌 Servidor MCP

### Comandos MCP
```bash
# Verificar status do servidor MCP
./claude-flow mcp status

# Iniciar servidor MCP
./claude-flow mcp start

# Parar servidor MCP
./claude-flow mcp stop

# Reiniciar servidor MCP
./claude-flow mcp restart

# Configurar MCP
./claude-flow mcp config

# Listar ferramentas MCP
./claude-flow mcp tools

# Testar conexão MCP
./claude-flow mcp test
```

---

## 📁 Gerenciamento de Projetos

### Comandos de Projeto
```bash
# Criar novo projeto
./claude-flow project create "API Project" --type web-app

# Listar projetos
./claude-flow project list

# Mostrar detalhes do projeto
./claude-flow project show "API Project"

# Atualizar projeto
./claude-flow project update "API Project" --description "Updated description"

# Arquivar projeto
./claude-flow project archive "Old Project"

# Deletar projeto
./claude-flow project delete "Unused Project"
```

### Templates de Projeto
```bash
# Listar templates disponíveis
./claude-flow project templates

# Criar projeto a partir de template
./claude-flow project create "My App" --template react-app
./claude-flow project create "My API" --template express-api

# Criar template customizado
./claude-flow project template create "my-template" --from "existing-project"
```

---

## 🚀 Deploy e Cloud

### Comandos de Deploy
```bash
# Criar deploy
./claude-flow deploy create "v1.2.0"

# Deploy com estratégia específica
./claude-flow deploy create "v1.2.0" --strategy blue-green

# Listar deploys
./claude-flow deploy list

# Status do deploy
./claude-flow deploy status deploy-id-123

# Rollback de deploy
./claude-flow deploy rollback "v1.1.0"
```

### Gerenciamento de Cloud
```bash
# Listar recursos de cloud
./claude-flow cloud resources list

# Criar recurso
./claude-flow cloud resources create "web-server" compute

# Status dos recursos
./claude-flow cloud resources status

# Pro dimensionar recursos
./claude-flow cloud scale "web-server" --instances 5

# Backup
./claude-flow cloud backup create "database-backup"
```

---

## 🛡️ Segurança

### Comandos de Segurança
```bash
# Scan de segurança
./claude-flow security scan "Vulnerability Check" ./src

# Auditoria de dependências
./claude-flow security audit dependencies

# Verificar compliance
./claude-flow security compliance --framework SOC2

# Relatório de segurança
./claude-flow security report --format pdf

# Atualizar políticas de segurança
./claude-flow security policies update
```

---

## 📈 Analytics

### Comandos de Analytics
```bash
# Insights de performance
./claude-flow analytics insights --timerange 7d

# Relatório de uso
./claude-flow analytics usage --period monthly

# Métricas de produtividade
./claude-flow analytics productivity

# Análise de trends
./claude-flow analytics trends --category development

# Exportar relatório
./claude-flow analytics export --format json
```

---

## ⚡ BatchTool

### Execução Paralela
```bash
# Executar comandos em paralelo
batchtool run --parallel \
"./claude-flow sparc run architect 'design user auth'" \
"./claude-flow sparc run code 'implement login API'" \
"./claude-flow sparc run tdd 'create auth tests'" \
"./claude-flow sparc run security-review 'audit auth flow'"

# BatchTool com limite de concorrência
batchtool run --parallel --max-concurrent 3 \
"./claude-flow sparc run code 'feature-1'" \
"./claude-flow sparc run code 'feature-2'" \
"./claude-flow sparc run code 'feature-3'" \
"./claude-flow sparc run code 'feature-4'"

# BatchTool com timeout
batchtool run --parallel --timeout 1800 \
"./claude-flow sparc run architect 'design system'" \
"./claude-flow sparc run code 'implement core'"
```

---

## 🔧 Solução de Problemas

### Diagnóstico
```bash
# Verificar saúde do sistema
./claude-flow health

# Diagnóstico completo
./claude-flow diagnose

# Verificar dependências
./claude-flow check dependencies

# Verificar configuração
./claude-flow check config

# Verificar conectividade
./claude-flow check connectivity
```

### Limpeza e Reset
```bash
# Limpar cache
./claude-flow clean cache

# Limpar logs antigos
./claude-flow clean logs --older-than 7d

# Reset completo (cuidado!)
./claude-flow reset --confirm

# Reparar instalação
./claude-flow repair

# Atualizar sistema
./claude-flow update
```

### Comandos de Debug
```bash
# Modo debug
./claude-flow --debug start

# Logs verbosos
./claude-flow --verbose logs

# Trace de execução
./claude-flow --trace sparc run code "test"

# Perfil de performance
./claude-flow --profile monitor
```

---

## 🎯 Exemplos de Fluxos Completos

### Desenvolvimento Full-Stack
```bash
# 1. Planejamento
./claude-flow sparc run ask "research best practices for e-commerce development"
./claude-flow memory store research "Key findings from e-commerce research"

# 2. Arquitetura
./claude-flow sparc run architect "design scalable e-commerce architecture"
./claude-flow memory store architecture "Microservices with API Gateway"

# 3. Desenvolvimento paralelo
batchtool run --parallel \
"./claude-flow sparc run code 'implement user service'" \
"./claude-flow sparc run code 'implement product service'" \
"./claude-flow sparc run code 'implement order service'"

# 4. Testes
./claude-flow sparc run tdd "create comprehensive test suite"

# 5. Segurança
./claude-flow sparc run security-review "audit entire system"

# 6. Deploy
./claude-flow sparc run devops "setup production deployment"
```

### Projeto com Swarm
```bash
# Deploy de swarm para desenvolvimento completo
./claude-flow swarm "Build social media platform with real-time chat, user profiles, and content feeds" \
--strategy full-stack \
--max-agents 8 \
--parallel \
--monitor

# Monitorar progresso
./claude-flow monitor --dashboard
```

### Migração de Sistema Legado
```bash
# Análise do sistema atual
./claude-flow sparc run ask "analyze legacy PHP system for React migration"

# Planejamento da migração
./claude-flow sparc run architect "design migration strategy from PHP to React/Node.js"

# Execução da migração por partes
./claude-flow swarm "Migrate user authentication module from PHP to Node.js" --strategy migration
./claude-flow swarm "Migrate admin panel from PHP to React" --strategy migration

# Testes de integração
./claude-flow sparc run integration "test migrated components with legacy system"
```

---

## 📞 Comandos de Ajuda

### Ajuda Geral
```bash
# Ajuda principal
./claude-flow help
./claude-flow --help

# Ajuda por comando
./claude-flow sparc --help
./claude-flow swarm --help
./claude-flow agent --help

# Exemplos de uso
./claude-flow examples

# Documentação
./claude-flow docs

# Tutorial interativo
./claude-flow tutorial
```