import { useEffect, useRef } from 'react';
import { ChatMessage } from '@/types';
import { MessageBubble } from './MessageBubble';
import { clsx } from 'clsx';

interface ChatWindowProps {
  messages: ChatMessage[];
  isLoading?: boolean;
  className?: string;
}

export function ChatWindow({ messages, isLoading = false, className }: ChatWindowProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div 
      ref={containerRef}
      className={clsx(
        'flex-1 overflow-y-auto bg-white px-4 py-6 space-y-4',
        className
      )}
    >
      {messages.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <div className="mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-pv-blue-primary to-pv-cyan-500 rounded-full flex items-center justify-center mb-4 mx-auto">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-pv-gray-800 mb-2">
              Bem-vindo ao Chatbot da Prefeitura Virtual
            </h3>
            <p className="text-pv-gray-600 max-w-md">
              Faça perguntas sobre serviços municipais, consulte informações dos departamentos ou solicite relatórios específicos.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl">
            <button className="p-3 text-left bg-pv-blue-50 hover:bg-pv-blue-100 rounded-lg border border-pv-blue-200 transition-colors">
              <div className="font-medium text-pv-blue-800">💼 Consultar Departamentos</div>
              <div className="text-sm text-pv-blue-600">Ver informações dos departamentos municipais</div>
            </button>
            
            <button className="p-3 text-left bg-pv-cyan-50 hover:bg-pv-cyan-100 rounded-lg border border-pv-cyan-200 transition-colors">
              <div className="font-medium text-pv-cyan-800">👥 Buscar Funcionários</div>
              <div className="text-sm text-pv-cyan-600">Encontrar informações sobre servidores</div>
            </button>
            
            <button className="p-3 text-left bg-pv-yellow-50 hover:bg-pv-yellow-100 rounded-lg border border-pv-yellow-200 transition-colors">
              <div className="font-medium text-pv-yellow-800">📊 Gerar Relatórios</div>
              <div className="text-sm text-pv-yellow-600">Solicitar relatórios específicos</div>
            </button>
            
            <button className="p-3 text-left bg-pv-orange-50 hover:bg-pv-orange-100 rounded-lg border border-pv-orange-200 transition-colors">
              <div className="font-medium text-pv-orange-800">🔍 Busca Avançada</div>
              <div className="text-sm text-pv-orange-600">Fazer consultas personalizadas</div>
            </button>
          </div>
        </div>
      ) : (
        <>
          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-pv-cyan-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">IA</span>
                </div>
                <div className="bg-pv-gray-100 rounded-lg px-4 py-3 border border-pv-gray-200">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-pv-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-pv-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-pv-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
}