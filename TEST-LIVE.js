// Teste em tempo real - execute após iniciar o backend
const http = require('http');

function testLiveBackend() {
  console.log('🧪 TESTE EM TEMPO REAL - BACKEND WINDOWS');
  console.log('========================================');
  console.log('1. Certifique-se que o backend está rodando: cd backend && npm run dev');
  console.log('2. Aguarde a mensagem "Servidor rodando na porta 3001"');
  console.log('3. Execute este script novamente');
  console.log('');
  
  // Teste simples de conexão
  const testConnection = () => {
    const req = http.request('http://localhost:3001/health', { 
      method: 'GET',
      timeout: 5000 
    }, (res) => {
      console.log('✅ Backend está online!');
      
      // Agora teste o chat
      testChatEndpoint();
      
    });
    
    req.on('error', (error) => {
      console.log('❌ Backend ainda não está respondendo:', error.message);
      console.log('🔧 Verifique se:');
      console.log('   - Executou: cd backend && npm run dev');
      console.log('   - Aguardou a mensagem de servidor iniciado');
      console.log('   - Não há erros no console do backend');
      
      setTimeout(() => {
        console.log('\n🔄 Tentando novamente em 5 segundos...');
        testConnection();
      }, 5000);
    });
    
    req.end();
  };
  
  const testChatEndpoint = () => {
    console.log('\n🤖 Testando endpoint de chat...');
    
    const chatData = JSON.stringify({
      message: "Quantos protocolos temos em andamento?",
      secretaria: "administracao",
      userId: "test-user"
    });
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/chat/message',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(chatData)
      },
      timeout: 30000
    };
    
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        console.log('\n📊 Resposta do Chat:');
        console.log('Status:', res.statusCode);
        
        try {
          const response = JSON.parse(body);
          
          if (response.data && response.data.response) {
            const chatResponse = response.data.response;
            
            console.log('\n🤖 Resposta da IA:');
            console.log('='.repeat(60));
            console.log(chatResponse);
            console.log('='.repeat(60));
            
            // Verificar se contém dados PostgreSQL
            if (chatResponse.includes('não tenho acesso ao banco de dados') || 
                chatResponse.includes('não tenho acesso direto ao banco')) {
              console.log('\n❌ PROBLEMA CONFIRMADO: Ainda responde sem acesso ao BD');
              console.log('🔧 A integração PostgreSQL NÃO está funcionando no backend!');
              console.log('💡 Verifique logs do backend para erros PostgreSQL');
            } else if (chatResponse.includes('13.697') || chatResponse.includes('13697') ||
                      chatResponse.includes('26.760') || chatResponse.includes('26760')) {
              console.log('\n✅ SUCESSO TOTAL! Backend está usando dados PostgreSQL!');
              console.log('🎊 A integração funcionou perfeitamente!');
            } else {
              console.log('\n⚠️ Resposta diferente do esperado');
              console.log('🔍 Pode estar funcionando mas com dados diferentes');
            }
            
          } else {
            console.log('\n⚠️ Formato de resposta inesperado:', response);
          }
          
        } catch (error) {
          console.log('\n❌ Erro ao parse da resposta:', body);
        }
      });
    });
    
    req.on('error', (error) => {
      console.log('\n❌ Erro na requisição de chat:', error.message);
    });
    
    req.on('timeout', () => {
      console.log('\n⏰ Timeout na requisição - backend pode estar processando');
      req.destroy();
    });
    
    req.write(chatData);
    req.end();
  };
  
  // Iniciar teste
  testConnection();
}

testLiveBackend();