const { Client } = require('pg');

async function testPostgresIntegration() {
  console.log('🔍 Testando integração PostgreSQL...');
  
  const client = new Client({
    host: '*************',
    port: 5411,
    user: 'otto',
    password: 'otto',
    database: 'pv_valparaiso'
  });
  
  try {
    console.log('📡 Conectando ao PostgreSQL...');
    await client.connect();
    console.log('✅ Conectado com sucesso!');
    
    // Testar consultas básicas
    console.log('\n📊 Obtendo estatísticas gerais...');
    const stats = await Promise.all([
      client.query('SELECT COUNT(*) as total FROM protocolo_virtual_processos'),
      client.query('SELECT COUNT(*) as total FROM requisicao'),
      client.query('SELECT COUNT(*) as total FROM servicos'),
      client.query('SELECT COUNT(*) as total FROM departamentos')
    ]);
    
    console.log('📋 Resultados:');
    console.log(`- Protocolos: ${stats[0].rows[0].total}`);
    console.log(`- Requisições: ${stats[1].rows[0].total}`);
    console.log(`- Serviços: ${stats[2].rows[0].total}`);
    console.log(`- Departamentos: ${stats[3].rows[0].total}`);
    
    // Testar consulta de protocolos recentes
    console.log('\n🔍 Buscando protocolos recentes...');
    const protocolosResult = await client.query(`
      SELECT p.id_protocolo, p.data_protocolo, 
             COALESCE(a.descricao, 'Não informado') as assunto,
             COALESCE(s.descricao, 'Não informado') as situacao
      FROM protocolo_virtual_processos p
      LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
      LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
      ORDER BY p.data_protocolo DESC
      LIMIT 5
    `);
    
    console.log('📄 Protocolos recentes:');
    protocolosResult.rows.forEach((p, index) => {
      console.log(`${index + 1}. ${p.id_protocolo} - ${p.assunto} (${p.situacao})`);
    });
    
    // Testar busca de alvarás
    console.log('\n🏢 Buscando alvarás...');
    const alvarasResult = await client.query(`
      SELECT p.id_protocolo, a.descricao as assunto, s.descricao as situacao
      FROM protocolo_virtual_processos p
      LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
      LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
      WHERE LOWER(a.descricao) LIKE '%alvará%' 
         OR LOWER(a.descricao) LIKE '%licença%'
         OR LOWER(a.descricao) LIKE '%funcionamento%'
      ORDER BY p.data_protocolo DESC
      LIMIT 5
    `);
    
    console.log('🏛️ Alvarás encontrados:');
    if (alvarasResult.rows.length > 0) {
      alvarasResult.rows.forEach((a, index) => {
        console.log(`${index + 1}. ${a.id_protocolo} - ${a.assunto} (${a.situacao})`);
      });
    } else {
      console.log('ℹ️ Nenhum alvará encontrado com os termos específicos');
    }
    
    console.log('\n✅ Teste de integração PostgreSQL concluído com sucesso!');
    console.log('🎯 O chatbot agora terá acesso aos dados reais do banco');
    
  } catch (error) {
    console.error('❌ Erro no teste PostgreSQL:', error.message);
  } finally {
    await client.end();
    console.log('🔌 Conexão fechada');
  }
}

testPostgresIntegration();