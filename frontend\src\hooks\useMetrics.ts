import { useState, useEffect, useCallback } from 'react';

interface MetricsData {
  summary: {
    totalMessages: number;
    cacheHitRate: number;
    totalSavings: number;
    savingsPercentage: number;
    dailyCost: number;
    monthlyCostEstimate: number;
  };
  cache: {
    metrics: {
      totalRequests: number;
      cacheHits: number;
      cacheMisses: number;
      hitRate: number;
      totalSavings: number;
    };
  };
  realTimeCost: {
    perMessage: string;
    dailySpent: string;
    dailyBudget: string;
    monthlyEstimate: string;
  };
  discountInfo: {
    isActive: boolean;
    nextTime: string;
    potentialSavings: string;
  };
}

export function useMetrics() {
  const [metrics, setMetrics] = useState<MetricsData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:3001/api/chat/metrics');
      
      if (!response.ok) {
        throw new Error('Erro ao buscar métricas');
      }

      const data = await response.json();
      
      if (data.success) {
        setMetrics(data.data);
      } else {
        throw new Error(data.error || 'Erro desconhecido');
      }
    } catch (err) {
      console.error('Erro ao buscar métricas:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Buscar métricas na montagem do componente
  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  // Atualizar métricas a cada 30 segundos
  useEffect(() => {
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, [fetchMetrics]);

  return {
    metrics,
    isLoading,
    error,
    refetch: fetchMetrics
  };
}