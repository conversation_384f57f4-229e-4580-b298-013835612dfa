// Teste após correção da query SQL
const http = require('http');

console.log('🔧 TESTANDO APÓS CORREÇÃO DA QUERY SQL');
console.log('====================================');
console.log('Correção aplicada: Removido campo "requerente" inexistente');
console.log('');

async function testFixApplied() {
  // Usar timestamp único para evitar cache
  const timestamp = Date.now();
  const uniqueMessage = `Quantos protocolos em andamento após correção ${timestamp}?`;
  
  console.log('📝 Pergunta única:', uniqueMessage);
  console.log('⏳ Aguardando resposta...');
  
  try {
    const response = await makeRequest('http://localhost:3001/api/chat/message', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    }, {
      message: uniqueMessage,
      secretaria: "administracao", 
      userId: "test-fix-" + timestamp
    });
    
    console.log('\n📊 Resultado:');
    console.log('Status:', response.status);
    
    if (response.data.success) {
      const chatResponse = response.data.data.response;
      const cacheHit = response.data.cacheHit;
      
      console.log('Cache Hit:', cacheHit);
      console.log('\n🤖 RESPOSTA APÓS CORREÇÃO:');
      console.log('='.repeat(80));
      console.log(chatResponse);
      console.log('='.repeat(80));
      
      // Analisar resposta
      if (chatResponse.includes('não tenho acesso ao banco de dados') || 
          chatResponse.includes('não tenho acesso em tempo real')) {
        console.log('\n❌ AINDA SEM INTEGRAÇÃO POSTGRESQL');
        console.log('🔧 A correção não resolveu o problema');
        console.log('💡 Pode haver outros erros na integração');
      } else if (chatResponse.includes('26.760') || chatResponse.includes('13.697') ||
                chatResponse.includes('protocolos no sistema') || 
                chatResponse.includes('consultando nosso sistema')) {
        console.log('\n🎊 SUCESSO TOTAL! INTEGRAÇÃO POSTGRESQL FUNCIONANDO!');
        console.log('✅ A correção da query SQL resolveu o problema!');
        console.log('🚀 O chatbot agora acessa dados reais do PostgreSQL!');
      } else {
        console.log('\n⚠️ Resposta diferente - pode estar funcionando parcialmente');
        console.log('🔍 Verificar se contém dados específicos do município');
      }
      
      // Verificar metadados
      if (response.data.metadata) {
        console.log('\n📊 Metadados:');
        console.log('- Tempo de processamento:', response.data.metadata.processingTime, 'ms');
        console.log('- Tokens:', response.data.metadata.tokens?.total || 'N/A');
        console.log('- Custo:', response.data.data?.cost || 'N/A');
      }
      
    } else {
      console.log('❌ Erro na API:', response.data.error);
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
  }
  
  console.log('\n🎯 PRÓXIMOS PASSOS:');
  if (true) { // Sempre mostrar próximos passos
    console.log('========================');
    console.log('1. Se funcionou: ✅ Integração PostgreSQL está completa!');
    console.log('2. Se não funcionou: Verificar logs do backend para outros erros');
    console.log('3. Testar outras perguntas sobre protocolos');
    console.log('4. Integrar com frontend para teste completo');
  }
}

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const reqOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.pathname,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 20000
    };
    
    const req = http.request(reqOptions, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsedBody });
        } catch {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

testFixApplied();