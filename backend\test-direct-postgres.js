// Teste direto da integração PostgreSQL no deepSeekService
const { Client } = require('pg');

// Simular a lógica do deepSeekService.ts
async function testPostgresIntegration() {
  console.log('🧪 Testando lógica PostgreSQL diretamente...\n');
  
  // Simular mensagem do usuário
  const message = "Quantos protocolos de alvará temos em andamento?";
  const messageQuery = message.toLowerCase();
  
  console.log(`📤 Mensagem: "${message}"`);
  
  // Detectar intenção (exatamente como no código)
  const isProtocolQuery = messageQuery.includes('protocolo') || 
                         messageQuery.includes('processo') ||
                         messageQuery.includes('alvará') ||
                         messageQuery.includes('licença') ||
                         messageQuery.includes('licenca') ||
                         messageQuery.includes('andamento') ||
                         messageQuery.includes('situação') ||
                         messageQuery.includes('situacao');
  
  console.log(`🔍 Detectou consulta de protocolos: ${isProtocolQuery}`);
  
  if (isProtocolQuery) {
    console.log('\n📡 Iniciando consulta PostgreSQL...');
    
    const client = new Client({
      host: '*************',
      port: 5411,
      user: 'otto',
      password: 'otto',
      database: 'pv_valparaiso'
    });
    
    try {
      await client.connect();
      console.log('✅ Conectado ao PostgreSQL');
      
      // Obter estatísticas (como no PostgreSQLQueryService)
      console.log('📊 Buscando estatísticas...');
      const estatisticas = await client.query('SELECT COUNT(*) as total FROM protocolo_virtual_processos');
      
      // Buscar protocolos recentes
      console.log('📋 Buscando protocolos de alvará...');
      const protocolosResult = await client.query(`
        SELECT p.id_protocolo, p.data_protocolo, 
               COALESCE(a.descricao, 'Não informado') as assunto,
               COALESCE(s.descricao, 'Não informado') as situacao
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        WHERE LOWER(a.descricao) LIKE '%alvará%'
        ORDER BY p.data_protocolo DESC
        LIMIT 5
      `);
      
      // Buscar alvarás em andamento
      console.log('🏛️ Contando alvarás em andamento...');
      const alvarasAndamento = await client.query(`
        SELECT COUNT(*) as total 
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        WHERE LOWER(a.descricao) LIKE '%alvará%' 
          AND LOWER(s.descricao) LIKE '%andamento%'
      `);
      
      const totalProtocolos = estatisticas.rows[0].total;
      const protocolosAlvara = protocolosResult.rows;
      const alvarasEmAndamento = alvarasAndamento.rows[0].total;
      
      console.log('\n✅ DADOS OBTIDOS:');
      console.log(`   Total de protocolos: ${totalProtocolos}`);
      console.log(`   Alvarás encontrados: ${protocolosAlvara.length}`);
      console.log(`   Alvarás em andamento: ${alvarasEmAndamento}`);
      
      // Construir prompt que seria enviado para a IA
      console.log('\n🎯 PROMPT QUE SERIA CONSTRUÍDO:');
      console.log('═'.repeat(80));
      
      const dadosRelevantes = {
        estatisticas: { protocolos: parseInt(totalProtocolos) },
        protocolosRecentes: protocolosAlvara,
        alvaras: protocolosAlvara,
        tipoConsulta: 'protocolos'
      };
      
      let systemPrompt = `**DADOS MUNICIPAIS ATUALIZADOS EM TEMPO REAL (${new Date().toLocaleDateString('pt-BR')}):**
- 📊 Total de protocolos no sistema: ${dadosRelevantes.estatisticas.protocolos.toLocaleString('pt-BR')}
- 🏛️ Alvarás em andamento: ${alvarasEmAndamento}

**ALVARÁS RECENTES ENCONTRADOS:**
${dadosRelevantes.protocolosRecentes.map((p, index) => 
  `${index + 1}. Protocolo ${p.id_protocolo}:
   - Assunto: ${p.assunto}
   - Situação: ${p.situacao}
   - Data: ${new Date(p.data_protocolo).toLocaleDateString('pt-BR')}`
).join('\n')}

**INSTRUÇÕES PARA USO DOS DADOS REAIS:**
- SEMPRE use estes dados atualizados em suas respostas
- Seja específico e preciso com números e datas
- Cite protocolos específicos quando relevante
- Nunca diga "não tenho acesso ao banco de dados" - você TEM acesso aos dados acima
- Use os números reais para dar respostas concretas e úteis`;

      console.log(systemPrompt);
      console.log('═'.repeat(80));
      
      console.log('\n🎊 CONCLUSÃO:');
      console.log('✅ A lógica PostgreSQL está FUNCIONANDO perfeitamente!');
      console.log('✅ Os dados são obtidos com sucesso');
      console.log('✅ O prompt é construído corretamente');
      console.log('\n❌ PROBLEMA: O código não está sendo executado no deepSeekService.ts');
      console.log('💡 SOLUÇÃO: Verificar se o arquivo foi salvo e recompilado');
      
    } catch (error) {
      console.error('❌ Erro PostgreSQL:', error.message);
    } finally {
      await client.end();
    }
  }
}

testPostgresIntegration();