#!/bin/bash

# Claude-Flow Mock Script - Simula funcionalidades básicas do Claude-Flow
# Este script serve como um wrapper temporário até que possamos atualizar o Node.js

echo "Claude-Flow Mock v1.0 - Simulador de funcionalidades"
echo "=================================="

case "$1" in
  "status")
    echo "Sistema: Ativo (modo simulado)"
    echo "Node.js: $(node --version)"
    echo "NPM: $(npm --version)"
    echo "Diretório: $(pwd)"
    echo "Modos SPARC: 17 disponíveis"
    ;;
    
  "sparc")
    if [ "$2" = "modes" ]; then
      echo "Modos SPARC disponíveis:"
      echo "- architect: Design de arquitetura"
      echo "- code: Desenvolvimento"
      echo "- tdd: Testes"
      echo "- security-review: Auditoria"
      echo "- integration: Integração"
      echo "- devops: Deploy/CI-CD"
      echo "- ask: Pesquisa"
      echo "- research: Análise profunda"
      echo "- debug: Debugging"
      echo "- review: Code review"
      echo "- optimize: Otimização"
      echo "- document: Documentação"
      echo "- migrate: Migração"
      echo "- refactor: Refatoração"
      echo "- test: Testes gerais"
      echo "- analyze: Análise"
      echo "- design: Design patterns"
    else
      echo "Executando modo SPARC: $2"
      echo "Comando: $3"
      echo "[SIMULADO] Resultado seria processado pelo Claude-Flow"
    fi
    ;;
    
  "memory")
    case "$2" in
      "store")
        echo "[SIMULADO] Armazenando na memória: $3 = $4"
        echo "$3=$4" >> .claude-flow-memory.txt
        ;;
      "query")
        echo "[SIMULADO] Buscando na memória: $3"
        grep -i "$3" .claude-flow-memory.txt 2>/dev/null || echo "Nenhum resultado encontrado"
        ;;
      "list")
        echo "[SIMULADO] Memória armazenada:"
        cat .claude-flow-memory.txt 2>/dev/null || echo "Memória vazia"
        ;;
    esac
    ;;
    
  "start")
    echo "[SIMULADO] Claude-Flow iniciado"
    if [ "$2" = "--ui" ]; then
      echo "Interface web simulada na porta $4"
    fi
    ;;
    
  *)
    echo "Claude-Flow Mock - Comandos disponíveis:"
    echo "  ./claude-flow-mock.sh status              - Status do sistema"
    echo "  ./claude-flow-mock.sh sparc modes         - Listar modos SPARC"
    echo "  ./claude-flow-mock.sh sparc [modo] [cmd]  - Executar modo SPARC"
    echo "  ./claude-flow-mock.sh memory store k v    - Armazenar na memória"
    echo "  ./claude-flow-mock.sh memory query termo  - Buscar na memória"
    echo "  ./claude-flow-mock.sh memory list         - Listar memória"
    echo "  ./claude-flow-mock.sh start [--ui]        - Iniciar sistema"
    ;;
esac