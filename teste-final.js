// TESTE FINAL DA INTEGRAÇÃO POSTGRESQL
console.log('🎯 TESTE FINAL - INTEGRAÇÃO POSTGRESQL FUNCIONANDO!\n');

const http = require('http');

const postData = JSON.stringify({
  message: `Teste final: quantos protocolos municipais temos hoje ${Date.now()}?`,
  userId: `test-final-${Date.now()}`,
  secretaria: "administracao"
});

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/chat/message',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('📤 Enviando pergunta única para evitar cache...');
console.log('⏳ Aguardando resposta da versão JavaScript...\n');

const req = http.request(options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      
      if (response.success) {
        console.log('✅ RESPOSTA RECEBIDA!');
        
        const content = response.response || response.data?.response || response.data?.content || '';
        
        console.log('\n📱 RESPOSTA DO CHATBOT:');
        console.log('═'.repeat(100));
        console.log(content);
        console.log('═'.repeat(100));
        
        // Verificar fonte da resposta
        const source = response.data?.source || response.source || 'desconhecida';
        console.log(`\n🔧 Fonte da resposta: ${source}`);
        console.log(`📊 Cache Hit: ${response.cacheHit ? 'SIM' : 'NÃO'}`);
        
        // Análise da integração
        const contentLower = content.toLowerCase();
        const hasSpecificNumbers = /\d{1,3}(?:,\d{3})*|\d{4,}/.test(content);
        const hasProtocolNumbers = /20\d{10}/.test(content);
        const hasNoAccessMessage = contentLower.includes('não tenho acesso') || 
                                   contentLower.includes('no momento, não tenho') ||
                                   contentLower.includes('infelizmente, não tenho');
        
        console.log('\n🔍 ANÁLISE FINAL:');
        console.log('─'.repeat(60));
        
        if (source === 'javascript-version') {
          console.log('✅ USANDO VERSÃO JAVASCRIPT (correto!)');
        } else {
          console.log('⚠️ Ainda usando versão TypeScript (pode ter problemas)');
        }
        
        if (!response.cacheHit) {
          console.log('✅ Requisição NOVA (não veio do cache)');
        } else {
          console.log('❌ Ainda veio do cache');
        }
        
        if (hasSpecificNumbers) {
          console.log('✅ Contém números específicos');
        } else {
          console.log('❌ Não contém números específicos');
        }
        
        if (hasProtocolNumbers) {
          console.log('✅ Contém números de protocolo reais! 🎉');
        } else {
          console.log('⚠️ Não contém números de protocolo');
        }
        
        if (hasNoAccessMessage) {
          console.log('❌ AINDA contém "não tenho acesso"');
        } else {
          console.log('✅ NÃO contém "não tenho acesso" 🎊');
        }
        
        // Resultado final
        console.log('\n🎯 RESULTADO FINAL:');
        console.log('═'.repeat(80));
        
        if (source === 'javascript-version' && !hasNoAccessMessage && hasSpecificNumbers) {
          console.log('🎊🎊🎊 INTEGRAÇÃO POSTGRESQL FUNCIONANDO PERFEITAMENTE! 🎊🎊🎊');
          console.log('✅ Versão JavaScript ativa');
          console.log('✅ Dados reais sendo utilizados');
          console.log('✅ Sem mensagens de "não tenho acesso"');
          console.log('✅ MISSÃO CUMPRIDA! 🚀');
        } else if (hasNoAccessMessage) {
          console.log('🔴 AINDA NÃO FUNCIONOU');
          console.log('❌ Ainda contém "não tenho acesso ao banco"');
          console.log('💡 Necessário verificar os logs do servidor backend');
        } else {
          console.log('🟡 PARCIALMENTE FUNCIONANDO');
          console.log('✅ Sem "não tenho acesso"');
          console.log('⚠️ Mas pode estar usando dados genéricos');
        }
        
      } else {
        console.log('❌ Erro na resposta:', response.error);
      }
      
    } catch (error) {
      console.log('❌ Erro ao parsear JSON:', error.message);
      console.log('Resposta bruta:', data);
    }
  });
});

req.on('error', (error) => {
  console.log('❌ Erro de conexão:', error.message);
  console.log('\n🔧 Certifique-se de que:');
  console.log('1. O servidor backend está rodando na porta 3001');
  console.log('2. Execute este teste no Windows (PowerShell)');
  console.log('3. O servidor foi reiniciado após as modificações');
});

req.write(postData);
req.end();

// Timeout
setTimeout(() => {
  console.log('\n⏰ Timeout: Teste demorou mais de 45 segundos');
  process.exit(1);
}, 45000);