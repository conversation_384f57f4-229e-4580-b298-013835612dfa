'use client';

import { useState } from 'react';
import { Logo } from '@/components/ui';
import { ChatWindow, MessageInput } from '@/components/chat';
import { MetricCard, SystemStatus, LogsViewer } from '@/components/admin';
import { useChat } from '@/hooks/useChat';
import { useMetrics } from '@/hooks/useMetrics';
import { usePostgresMetrics } from '@/hooks/usePostgresData';

export default function AdminPage() {
  const { messages, isLoading, sendMessage, clearMessages } = useChat();
  const { metrics, isLoading: metricsLoading } = useMetrics();
  const { 
    metrics: postgresMetrics, 
    protocolosRecentes, 
    alvarasRecentes,
    topDepartamentos,
    isConnected: postgresConnected,
    loading: postgresLoading
  } = usePostgresMetrics();

  const handleSendMessage = async (message: string) => {
    try {
      await sendMessage({ message });
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  const handleClearChat = () => {
    clearMessages();
  };

  return (
    <div className="min-h-screen bg-pv-gray-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-pv-blue-primary via-pv-blue-secondary to-pv-cyan-500 text-white shadow-lg">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Logo variant="vertical" size="sm" />
              <div>
                <h1 className="text-2xl font-bold">Dashboard Administrativo</h1>
                <p className="text-pv-blue-100">Sistema de Monitoramento - Prefeitura Virtual</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* System Status Indicator */}
              <div className="flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm">Sistema Operacional</span>
              </div>
              
              {/* Admin Controls */}
              <div className="flex space-x-2">
                <button className="p-2 hover:bg-white/10 rounded-lg transition-colors" title="Configurações">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </button>
                
                <button className="p-2 hover:bg-white/10 rounded-lg transition-colors" title="Relatórios">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="p-6 space-y-6">
        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Custo Hoje"
            value={metrics?.realTimeCost?.dailySpent || '$0.00'}
            subtitle="De um orçamento de $50.00"
            variant="cost"
            trend={{ value: 12, isPositive: false }}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            }
          />
          
          <MetricCard
            title="Mensagens Hoje"
            value={metrics?.summary?.totalMessages || 0}
            subtitle="Cache hit: 68%"
            variant="performance"
            trend={{ value: 23, isPositive: true }}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            }
          />
          
          <MetricCard
            title="Cache Hit Rate"
            value={`${metrics?.cache?.metrics?.hitRate?.toFixed(1) || '0.0'}%`}
            subtitle="Performance excelente"
            variant="cache"
            trend={{ value: 5, isPositive: true }}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            }
          />
          
          <MetricCard
            title="Economia Gerada"
            value={`$${metrics?.summary?.totalSavings?.toFixed(2) || '0.00'}`}
            subtitle={`${metrics?.summary?.savingsPercentage?.toFixed(1) || '0.0'}% de economia`}
            variant="savings"
            trend={{ value: 45, isPositive: true }}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            }
          />
        </div>

        {/* PostgreSQL Database Metrics */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-pv-gray-900">Dados Municipais (Tempo Real)</h2>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${postgresConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm text-pv-gray-600">
                PostgreSQL {postgresConnected ? 'Conectado' : 'Desconectado'}
              </span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Protocolos Ativos"
              value={postgresMetrics?.totalProtocolos?.toLocaleString('pt-BR') || '0'}
              subtitle="Total no sistema"
              variant="performance"
              trend={{ value: 2, isPositive: true }}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              }
            />
            
            <MetricCard
              title="Em Andamento"
              value={postgresMetrics?.protocolosAndamento?.toLocaleString('pt-BR') || '0'}
              subtitle="Aguardando processamento"
              variant="cost"
              trend={{ value: 5, isPositive: false }}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
            />
            
            <MetricCard
              title="Requisições"
              value={postgresMetrics?.totalRequisicoes?.toLocaleString('pt-BR') || '0'}
              subtitle="Total processadas"
              variant="cache"
              trend={{ value: 15, isPositive: true }}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                </svg>
              }
            />
            
            <MetricCard
              title="Taxa de Conclusão"
              value={`${postgresMetrics?.taxaConclusao || 0}%`}
              subtitle="Eficiência do sistema"
              variant="savings"
              trend={{ value: 8, isPositive: true }}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
            />
          </div>
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Chat Admin - Takes 2 columns */}
          <div className="xl:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-pv-gray-200 h-[600px] flex flex-col">
              {/* Chat Header */}
              <div className="border-b border-pv-gray-200 p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-pv-gray-800 flex items-center space-x-2">
                      <svg className="w-5 h-5 text-pv-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      <span>Chat Administrativo</span>
                      <span className="px-2 py-1 bg-pv-orange-100 text-pv-orange-800 text-xs font-medium rounded-full">
                        ADMIN
                      </span>
                    </h3>
                    <p className="text-sm text-pv-gray-600">
                      Acesso completo ao banco de dados e sistema
                    </p>
                  </div>
                  
                  <button 
                    onClick={handleClearChat}
                    className="p-2 text-pv-gray-500 hover:text-pv-blue-primary hover:bg-pv-blue-50 rounded-lg transition-colors"
                    title="Limpar chat"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
              
              {/* Chat Messages */}
              <ChatWindow messages={messages} isLoading={isLoading} className="flex-1" />
              
              {/* Message Input */}
              <MessageInput 
                onSendMessage={handleSendMessage}
                isLoading={isLoading}
                placeholder="Consulte dados do PostgreSQL, gere relatórios ou monitore o sistema..."
              />
            </div>
          </div>

          {/* System Status */}
          <div className="space-y-6">
            <SystemStatus />
            
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm border border-pv-gray-200 p-4">
              <h3 className="text-lg font-semibold text-pv-gray-800 mb-4">Ações Rápidas</h3>
              <div className="space-y-2">
                <button className="w-full text-left p-3 hover:bg-pv-blue-50 rounded-lg transition-colors flex items-center space-x-3">
                  <svg className="w-5 h-5 text-pv-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span className="text-sm font-medium">Limpar Cache</span>
                </button>
                
                <button className="w-full text-left p-3 hover:bg-pv-cyan-50 rounded-lg transition-colors flex items-center space-x-3">
                  <svg className="w-5 h-5 text-pv-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <span className="text-sm font-medium">Gerar Relatório</span>
                </button>
                
                <button className="w-full text-left p-3 hover:bg-pv-yellow-50 rounded-lg transition-colors flex items-center space-x-3">
                  <svg className="w-5 h-5 text-pv-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707" />
                  </svg>
                  <span className="text-sm font-medium">Configurar Desconto</span>
                </button>
                
                <button className="w-full text-left p-3 hover:bg-pv-orange-50 rounded-lg transition-colors flex items-center space-x-3">
                  <svg className="w-5 h-5 text-pv-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                  </svg>
                  <span className="text-sm font-medium">Backup Sistema</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Protocols Section */}
        {protocolosRecentes && protocolosRecentes.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Protocolos Recentes */}
            <div className="bg-white rounded-lg shadow-sm border border-pv-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-pv-gray-800">Protocolos Recentes</h3>
                <svg className="w-5 h-5 text-pv-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="space-y-3">
                {protocolosRecentes.slice(0, 5).map((protocolo, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-pv-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium text-pv-gray-900">{protocolo.id_protocolo}</div>
                      <div className="text-sm text-pv-gray-600">{protocolo.assunto}</div>
                      <div className="text-xs text-pv-gray-500 mt-1">
                        {new Date(protocolo.data_protocolo).toLocaleDateString('pt-BR')}
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        protocolo.situacao.toLowerCase().includes('andamento') 
                          ? 'bg-yellow-100 text-yellow-800'
                          : protocolo.situacao.toLowerCase().includes('concluído') || protocolo.situacao.toLowerCase().includes('aprovado')
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {protocolo.situacao}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Alvarás Recentes */}
            {alvarasRecentes && alvarasRecentes.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-pv-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-pv-gray-800">Alvarás Recentes</h3>
                  <svg className="w-5 h-5 text-pv-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div className="space-y-3">
                  {alvarasRecentes.slice(0, 5).map((alvara, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-pv-cyan-50 rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium text-pv-gray-900">{alvara.id_protocolo}</div>
                        <div className="text-sm text-pv-gray-600">{alvara.assunto}</div>
                        <div className="text-xs text-pv-gray-500 mt-1">
                          {alvara.departamento}
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          alvara.situacao.toLowerCase().includes('andamento') 
                            ? 'bg-yellow-100 text-yellow-800'
                            : alvara.situacao.toLowerCase().includes('concluído') || alvara.situacao.toLowerCase().includes('aprovado')
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {alvara.situacao}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Top Departamentos Performance */}
        {topDepartamentos && topDepartamentos.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-pv-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-pv-gray-800">Performance por Departamento</h3>
              <svg className="w-5 h-5 text-pv-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {topDepartamentos.slice(0, 6).map((dept, index) => {
                const eficiencia = dept.total_protocolos > 0 
                  ? Math.round((dept.concluidos / dept.total_protocolos) * 100)
                  : 0;
                
                return (
                  <div key={index} className="p-4 bg-pv-gray-50 rounded-lg">
                    <div className="text-sm font-medium text-pv-gray-900 mb-2 truncate" title={dept.departamento}>
                      {dept.departamento}
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs">
                        <span className="text-pv-gray-600">Total:</span>
                        <span className="font-medium">{dept.total_protocolos}</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-pv-gray-600">Andamento:</span>
                        <span className="text-yellow-600 font-medium">{dept.em_andamento}</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-pv-gray-600">Concluídos:</span>
                        <span className="text-green-600 font-medium">{dept.concluidos}</span>
                      </div>
                      <div className="mt-2">
                        <div className="flex justify-between text-xs mb-1">
                          <span className="text-pv-gray-600">Eficiência:</span>
                          <span className="font-medium">{eficiencia}%</span>
                        </div>
                        <div className="w-full bg-pv-gray-200 rounded-full h-1.5">
                          <div 
                            className="bg-pv-blue-600 h-1.5 rounded-full" 
                            style={{ width: `${eficiencia}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Logs Section */}
        <LogsViewer />
      </div>
    </div>
  );
}