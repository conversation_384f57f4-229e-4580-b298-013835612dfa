'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Logo } from '@/components/ui';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // Redirecionar para o chat após 2 segundos
    const timer = setTimeout(() => {
      router.push('/chat');
    }, 2000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <main className="min-h-screen bg-gradient-to-br from-pv-blue-primary via-pv-blue-secondary to-pv-cyan-500 flex items-center justify-center p-6">
      <div className="text-center">
        <div className="mb-8">
          <Logo variant="vertical" size="lg" className="mx-auto mb-6" />
        </div>
        
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 max-w-md mx-auto">
          <h1 className="text-3xl font-bold text-white mb-4">
            Chatbot Inteligente
          </h1>
          <p className="text-pv-blue-100 text-lg mb-6">
            Sistema para Secretarias da Prefeitura de Valparaíso de Goiás
          </p>
          
          <div className="flex items-center justify-center space-x-2 text-white">
            <div className="w-2 h-2 bg-white rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            <span className="ml-3 text-sm">Carregando sistema...</span>
          </div>
        </div>
        
        <div className="mt-8 text-center">
          <p className="text-pv-blue-100 text-sm">
            Redirecionando para o chat em instantes...
          </p>
        </div>
      </div>
    </main>
  );
}