// FORÇA INTEGRAÇÃO POSTGRESQL IMEDIATAMENTE
const http = require('http');

console.log('🔧 FORÇANDO INTEGRAÇÃO POSTGRESQL AGORA');
console.log('=======================================');

async function forceIntegration() {
  console.log('1. 🗑️ Limpando cache...');
  
  // Primeiro, tentar limpar cache
  try {
    await makeRequest('http://localhost:3001/api/cache/clear', { method: 'POST' });
    console.log('✅ Cache limpo com sucesso');
  } catch (error) {
    console.log('⚠️ Não foi possível limpar cache:', error.message);
  }
  
  console.log('\n2. 🧪 Testando com mensagem única para evitar cache...');
  
  // Usar timestamp para evitar cache
  const timestamp = Date.now();
  const uniqueMessage = `Quantos protocolos em andamento temos hoje ${timestamp}?`;
  
  console.log('📝 Pergunta única:', uniqueMessage);
  
  const response = await makeRequest('http://localhost:3001/api/chat/message', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' }
  }, {
    message: uniqueMessage,
    secretaria: "administracao",
    userId: "test-user-" + timestamp,
    forceNoCache: true  // Tentar evitar cache
  });
  
  console.log('\n📊 Resultado:');
  console.log('Status:', response.status);
  
  if (response.data.success) {
    const chatResponse = response.data.data.response;
    const cacheHit = response.data.cacheHit;
    
    console.log('Cache Hit:', cacheHit);
    console.log('\n🤖 RESPOSTA:');
    console.log('='.repeat(80));
    console.log(chatResponse);
    console.log('='.repeat(80));
    
    if (cacheHit) {
      console.log('\n❌ AINDA USANDO CACHE!');
      console.log('🔧 O sistema está retornando resposta em cache');
      console.log('💡 Precisamos forçar bypass do cache');
    } else if (chatResponse.includes('não tenho acesso')) {
      console.log('\n❌ INTEGRAÇÃO POSTGRESQL NÃO FUNCIONA!');
      console.log('🔧 O deepSeekService não está acessando PostgreSQL');
      console.log('💡 Problema na execução do código de integração');
    } else if (chatResponse.includes('26.760') || chatResponse.includes('13.697')) {
      console.log('\n✅ SUCESSO! INTEGRAÇÃO POSTGRESQL FUNCIONANDO!');
      console.log('🎊 O chatbot agora acessa dados reais!');
    } else {
      console.log('\n⚠️ Resposta diferente do esperado');
      console.log('🔍 Pode estar funcionando parcialmente');
    }
    
  } else {
    console.log('❌ Erro na API:', response.data.error);
  }
  
  console.log('\n3. 🔍 Testando endpoint PostgreSQL direto...');
  
  try {
    const pgResponse = await makeRequest('http://localhost:3001/api/postgres/estatisticas', {
      method: 'GET'
    });
    
    if (pgResponse.status === 200 && pgResponse.data.success) {
      console.log('✅ PostgreSQL endpoint funciona:');
      console.log('- Protocolos:', pgResponse.data.data.geral.protocolos);
      console.log('- Requisições:', pgResponse.data.data.geral.requisicoes);
      console.log('');
      console.log('🔥 POSTGRESQL ESTÁ ACESSÍVEL!');
      console.log('🚨 O PROBLEMA É NO DEEPSEEK SERVICE!');
    } else {
      console.log('❌ PostgreSQL endpoint falhou');
    }
  } catch (error) {
    console.log('❌ PostgreSQL endpoint erro:', error.message);
  }
}

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const reqOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.pathname,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 15000
    };
    
    const req = http.request(reqOptions, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsedBody });
        } catch {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

forceIntegration().catch(console.error);