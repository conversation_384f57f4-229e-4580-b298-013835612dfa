import { useState, useEffect } from 'react';

interface PostgresStats {
  protocolos: number;
  requisicoes: number;
  servicos: number;
  departamentos: number;
}

interface PostgresDashboard {
  resumo: {
    totalProtocolos: number;
    totalRequisicoes: number;
    servicosAtivos: number;
    departamentosAtivos: number;
    protocolosAndamento: number;
    protocolosConcluidos: number;
    taxaConclusao: number;
  };
  protocolosRecentes: Array<{
    id_protocolo: string;
    assunto: string;
    situacao: string;
    data_protocolo: string;
    requerente: string;
  }>;
  alvarasRecentes: Array<{
    id_protocolo: string;
    assunto: string;
    situacao: string;
    departamento: string;
  }>;
  topDepartamentos: Array<{
    departamento: string;
    total_protocolos: number;
    em_andamento: number;
    concluidos: number;
  }>;
  ultimaAtualizacao: string;
}

interface PostgresHealth {
  status: 'CONECTADO' | 'DESCONECTADO';
  host: string;
  port: number;
  database: string;
  estatisticasRapidas?: PostgresStats;
}

export function usePostgresData() {
  const [stats, setStats] = useState<PostgresStats | null>(null);
  const [dashboard, setDashboard] = useState<PostgresDashboard | null>(null);
  const [health, setHealth] = useState<PostgresHealth | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE = process.env.NODE_ENV === 'production' 
    ? '/api' 
    : 'http://localhost:3001/api';

  /**
   * Buscar estatísticas básicas
   */
  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${API_BASE}/postgres/estatisticas`);
      const data = await response.json();
      
      if (data.success && data.data.geral) {
        setStats(data.data.geral);
      } else {
        throw new Error(data.error || 'Erro ao buscar estatísticas');
      }
    } catch (err: any) {
      console.error('Erro ao buscar estatísticas PostgreSQL:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Buscar dashboard completo
   */
  const fetchDashboard = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${API_BASE}/postgres/dashboard`);
      const data = await response.json();
      
      if (data.success) {
        setDashboard(data.data);
      } else {
        throw new Error(data.error || 'Erro ao buscar dashboard');
      }
    } catch (err: any) {
      console.error('Erro ao buscar dashboard PostgreSQL:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Verificar saúde da conexão
   */
  const checkHealth = async () => {
    try {
      const response = await fetch(`${API_BASE}/postgres/health`);
      const data = await response.json();
      
      if (data.success) {
        setHealth(data.data);
        return true;
      } else {
        setHealth({
          status: 'DESCONECTADO',
          host: '*************',
          port: 5411,
          database: 'pv_valparaiso'
        });
        return false;
      }
    } catch (err: any) {
      console.error('Erro ao verificar saúde PostgreSQL:', err);
      setHealth({
        status: 'DESCONECTADO',
        host: '*************',
        port: 5411,
        database: 'pv_valparaiso'
      });
      return false;
    }
  };

  /**
   * Buscar protocolos por termo
   */
  const searchProtocols = async (termo?: string, limite = 10) => {
    try {
      const params = new URLSearchParams();
      if (termo) params.append('termo', termo);
      params.append('limite', limite.toString());
      
      const response = await fetch(`${API_BASE}/postgres/protocolos/buscar?${params}`);
      const data = await response.json();
      
      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.error || 'Erro ao buscar protocolos');
      }
    } catch (err: any) {
      console.error('Erro ao buscar protocolos:', err);
      return [];
    }
  };

  /**
   * Buscar alvarás
   */
  const searchAlvaras = async (limite = 10) => {
    try {
      const response = await fetch(`${API_BASE}/postgres/protocolos/alvara?limite=${limite}`);
      const data = await response.json();
      
      if (data.success) {
        return data.data;
      } else {
        throw new Error(data.error || 'Erro ao buscar alvarás');
      }
    } catch (err: any) {
      console.error('Erro ao buscar alvarás:', err);
      return [];
    }
  };

  /**
   * Inicializar dados ao montar o componente
   */
  useEffect(() => {
    const initialize = async () => {
      const isHealthy = await checkHealth();
      if (isHealthy) {
        await Promise.all([
          fetchStats(),
          fetchDashboard()
        ]);
      }
    };

    initialize();
  }, []);

  /**
   * Atualizar dados periodicamente (apenas se conectado)
   */
  useEffect(() => {
    if (health?.status === 'CONECTADO') {
      const interval = setInterval(() => {
        fetchStats();
        fetchDashboard();
      }, 30000); // Atualizar a cada 30 segundos

      return () => clearInterval(interval);
    }
  }, [health?.status]);

  return {
    // Estados
    stats,
    dashboard,
    health,
    loading,
    error,
    
    // Funções
    refetchStats: fetchStats,
    refetchDashboard: fetchDashboard,
    checkHealth,
    searchProtocols,
    searchAlvaras,
    
    // Utilitários
    isConnected: health?.status === 'CONECTADO',
    lastUpdate: dashboard?.ultimaAtualizacao,
    
    // Dados formatados para exibição
    formattedStats: stats ? {
      totalProtocolos: stats.protocolos.toLocaleString('pt-BR'),
      totalRequisicoes: stats.requisicoes.toLocaleString('pt-BR'),
      servicosAtivos: stats.servicos,
      departamentosAtivos: stats.departamentos
    } : null
  };
}

/**
 * Hook específico para métricas do dashboard admin
 */
export function usePostgresMetrics() {
  const { dashboard, loading, error, refetchDashboard, isConnected } = usePostgresData();
  
  return {
    metrics: dashboard?.resumo,
    protocolosRecentes: dashboard?.protocolosRecentes,
    alvarasRecentes: dashboard?.alvarasRecentes,
    topDepartamentos: dashboard?.topDepartamentos,
    loading,
    error,
    isConnected,
    refresh: refetchDashboard,
    
    // Métricas calculadas
    percentualConclusao: dashboard?.resumo.taxaConclusao || 0,
    eficienciaMedia: dashboard?.topDepartamentos 
      ? Math.round(dashboard.topDepartamentos.reduce((acc, dept) => 
          acc + (dept.concluidos / (dept.total_protocolos || 1)) * 100, 0
        ) / dashboard.topDepartamentos.length)
      : 0
  };
}