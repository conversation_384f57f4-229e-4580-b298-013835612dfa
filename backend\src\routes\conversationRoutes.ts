import { Router } from 'express';
import ConversationController from '../controllers/conversationController';

const router = Router();

/**
 * @route GET /api/conversations/:userId
 * @desc Listar conversas do usuário
 * @access Public (por enquanto)
 */
router.get('/:userId', ConversationController.getConversations);

/**
 * @route POST /api/conversations
 * @desc Criar nova conversa
 * @access Public (por enquanto)
 * 
 * Body:
 * {
 *   "title": "Nova Conversa",
 *   "userId": "user123"
 * }
 */
router.post('/', ConversationController.createConversation);

/**
 * @route GET /api/conversations/:conversationId/messages
 * @desc Obter mensagens da conversa
 * @access Public (por enquanto)
 * 
 * Query params:
 * - page: número da página (default: 1)
 * - limit: mensagens por página (default: 50)
 */
router.get('/:conversationId/messages', ConversationController.getConversationMessages);

/**
 * @route DELETE /api/conversations/:conversationId
 * @desc Deletar conversa
 * @access Public (por enquanto)
 * 
 * Body:
 * {
 *   "userId": "user123"
 * }
 */
router.delete('/:conversationId', ConversationController.deleteConversation);

/**
 * @route PUT /api/conversations/:conversationId/title
 * @desc Atualizar título da conversa
 * @access Public (por enquanto)
 * 
 * Body:
 * {
 *   "title": "Novo Título",
 *   "userId": "user123"
 * }
 */
router.put('/:conversationId/title', ConversationController.updateConversationTitle);

/**
 * @route GET /api/conversations/:userId/search
 * @desc Buscar conversas
 * @access Public (por enquanto)
 * 
 * Query params:
 * - q: termo de busca
 */
router.get('/:userId/search', ConversationController.searchConversations);

export default router;