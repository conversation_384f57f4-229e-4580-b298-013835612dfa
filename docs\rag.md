# IMPLEMENTAÇÃO RAG VETORIAL - GUIA COMPLETO

**Data:** 23 de Janeiro de 2025  
**Status:** Pronto para implementação  
**Contexto:** Continuação do desenvolvimento do Chatbot Inteligente para Prefeitura de Valparaíso

---

## 🎯 OBJETIVO

Implementar sistema RAG (Retrieval-Augmented Generation) vetorial para busca semântica inteligente nos dados municipais da Prefeitura de Valparaíso de Goiás, melhorando a precisão das respostas do chatbot em 50-80%.

## 📊 SITUAÇÃO ATUAL (O QUE JÁ TEMOS)

### ✅ DADOS EXTRAÍDOS DO POSTGRESQL
- **111.396 cidadãos** cadastrados
- **1.513 servidores** públicos ativos  
- **500+ departamentos** mapeados
- **578 tabelas** analisadas
- **40+ serviços** municipais específicos identificados
- **20+ formulários** ativos catalogados
- **4 Leis Complementares** municipais mapeadas

### ✅ CONHECIMENTO ESTRUTURADO DISPONÍVEL

**Arquivo gerado:** `/backend/knowledge/municipal-knowledge.json`
```json
{
  "departamentos": [...], // 50 departamentos ativos
  "usuarios": [...],      // Estatísticas de usuários
  "estatisticas": {...},  // Dados gerais do município
  "estruturaOrganizacional": {...}, // Hierarquia municipal
  "conhecimentoTextual": [...],     // Conteúdo disponível
  "padroes": {...}       // Padrões identificados
}
```

**Arquivo gerado:** `/backend/knowledge/services-deep-analysis.json`
```json
{
  "servicos": [...],     // 20 serviços principais
  "formularios": [...],  // 20 formulários ativos
  "solicitacoes": [...], // Estrutura de solicitações
  "protocolos": [...],   // Sistema de protocolos
  "procedimentos": [...] // Procedimentos identificados
}
```

### ✅ PROMPTS JÁ EXPANDIDOS COM DADOS REAIS

**Arquivo:** `/backend/src/services/deepSeekService.ts`

Prompts melhorados para todas as secretarias:
- **Administração**: 587-933 tokens (vs ~100 antes)
- **Obras**: 830-1351 tokens com 4 leis específicas  
- **Finanças**: 550-940 tokens com sistema real
- **Meio Ambiente**: 689-1196 tokens com serviços específicos
- **Assistência Social**: 656-1285 tokens com procedimentos detalhados

### ✅ TESTES REALIZADOS - 100% SUCESSO

**Arquivo:** `/backend/src/scripts/test-improved-prompts.ts`

Resultados dos testes:
- Taxa de sucesso: **100%** (6/6 testes passaram)
- Precisão média: **68.7%** (vs ~20% antes)
- Tempo resposta: 22-40s (contextualizado)
- Custo: $0.000270-$0.000435 por consulta

---

## 🚀 IMPLEMENTAÇÃO RAG - ROADMAP DETALHADO

### FASE 1: RAG BÁSICO (4-6 horas de implementação)

#### **1.1 Preparação dos Dados (1 hora)**

**Objetivo:** Estruturar conhecimento extraído em chunks semânticos para vetorização.

**Ações necessárias:**

1. **Criar script de chunking inteligente:**
```typescript
// Arquivo a criar: /backend/src/services/ragDataPreparation.ts

interface DocumentChunk {
  id: string;
  content: string;
  metadata: {
    secretaria: string;
    tipo: 'servico' | 'departamento' | 'formulario' | 'lei' | 'procedimento';
    nivel_acesso: 'publico' | 'servidor' | 'admin';
    relevancia: number;
    keywords: string[];
  };
}
```

2. **Estruturar dados em chunks:**
   - Departamentos → 1 chunk por departamento (nome, descrição, contato)
   - Serviços → 1 chunk por serviço (nome, procedimento, documentos)
   - Formulários → 1 chunk por formulário (nome, descrição, uso)
   - Leis → 1 chunk por lei (número, aplicação, área)
   - Procedimentos → 1 chunk por procedimento (passo-a-passo)

3. **Adicionar metadata inteligente:**
   - `secretaria`: Para filtering por área
   - `tipo`: Para categorização
   - `nivel_acesso`: Para controle de acesso
   - `keywords`: Para busca híbrida

#### **1.2 Setup da Base Vetorial (2 horas)**

**Objetivo:** Configurar Chroma DB com OpenAI embeddings.

**Ações necessárias:**

1. **Instalar dependências:**
```bash
npm install chromadb openai
npm install @types/node --save-dev
```

2. **Criar serviço RAG:**
```typescript
// Arquivo a criar: /backend/src/services/ragService.ts

import { ChromaApi, OpenAIEmbeddingFunction } from 'chromadb';
import OpenAI from 'openai';

class RAGService {
  private chromaClient: ChromaApi;
  private collection: any;
  private openai: OpenAI;
  private embedder: OpenAIEmbeddingFunction;

  constructor() {
    // Configuração do Chroma
    // Configuração do OpenAI
    // Inicialização da collection
  }

  async addDocuments(chunks: DocumentChunk[]): Promise<void> {
    // Vetorizar e adicionar documentos
  }

  async searchSimilar(query: string, filters?: any): Promise<DocumentChunk[]> {
    // Busca semântica com filtering
  }

  async hybridSearch(query: string, secretaria?: string): Promise<DocumentChunk[]> {
    // Busca híbrida: semântica + keyword
  }
}
```

3. **Script de inicialização:**
```typescript
// Arquivo a criar: /backend/src/scripts/initialize-rag.ts

// Script para:
// - Criar collection no Chroma
// - Vetorizar todos os chunks
// - Testar busca semântica
// - Validar performance
```

#### **1.3 Integração com DeepSeek (1 hora)**

**Objetivo:** Modificar deepSeekService para usar RAG.

**Ações necessárias:**

1. **Modificar `deepSeekService.ts`:**
```typescript
// Em /backend/src/services/deepSeekService.ts

import { ragService } from './ragService';

export async function processMessage(
  message: string,
  context: ConversationContext
): Promise<DeepSeekResponse> {
  try {
    // 1. BUSCAR DOCUMENTOS RELEVANTES VIA RAG
    const relevantDocs = await ragService.hybridSearch(
      message, 
      context.secretaria
    );

    // 2. CONSTRUIR CONTEXTO HÍBRIDO
    const ragContext = relevantDocs.map(doc => doc.content).join('\n\n');
    const systemPrompt = getSystemPrompt(context.secretaria);
    const hybridPrompt = `${systemPrompt}\n\n**DOCUMENTOS RELEVANTES:**\n${ragContext}`;

    // 3. PROCESSAR COM DEEPSEEK
    const messages = [
      { role: 'system', content: hybridPrompt },
      { role: 'user', content: message }
    ];

    // ... resto da lógica existente
  } catch (error) {
    // Fallback para sistema atual se RAG falhar
  }
}
```

2. **Sistema de fallback:** Manter funcionamento normal se RAG não disponível

3. **Cache de embeddings:** Para economizar custos de vetorização

#### **1.4 Testes e Validação (2 horas)**

**Objetivo:** Comparar performance RAG vs não-RAG.

**Ações necessárias:**

1. **Criar script de teste comparativo:**
```typescript
// Arquivo a criar: /backend/src/scripts/test-rag-comparison.ts

// Testes lado-a-lado:
// - Mesma pergunta com RAG e sem RAG
// - Métricas: precisão, tempo, custo, relevância
// - Casos de teste por secretaria
```

2. **Otimizar parâmetros:**
   - Número de documentos recuperados (top-k)
   - Threshold de similaridade
   - Tamanho dos chunks
   - Estratégia de filtering

3. **Validar com casos reais:**
   - Usar perguntas típicas de cada secretaria
   - Medir melhoria de precisão
   - Identificar gaps de conhecimento

---

## 📁 ESTRUTURA DE ARQUIVOS NECESSÁRIA

### Novos arquivos a criar:

```
/backend/src/services/
├── ragService.ts              # Serviço principal RAG
├── ragDataPreparation.ts      # Preparação e chunking
└── ragEmbeddings.ts          # Gerenciamento de embeddings

/backend/src/scripts/
├── initialize-rag.ts         # Inicialização da base vetorial
├── test-rag-comparison.ts    # Testes comparativos
└── update-rag-knowledge.ts   # Atualização de conhecimento

/backend/rag-data/
├── chunks/                   # Chunks estruturados
├── embeddings/              # Cache de embeddings
└── chroma-db/              # Base vetorial local
```

### Arquivos a modificar:

```
/backend/src/services/deepSeekService.ts  # Integração RAG
/backend/package.json                     # Novas dependências
/backend/.env                            # Configurações RAG
```

---

## ⚙️ CONFIGURAÇÕES TÉCNICAS

### Variáveis de Ambiente (.env):

```bash
# RAG Configuration
RAG_ENABLED=true
RAG_TOP_K=3
RAG_SIMILARITY_THRESHOLD=0.7
RAG_CHUNK_SIZE=500
RAG_CHUNK_OVERLAP=50

# Chroma DB
CHROMA_HOST=localhost
CHROMA_PORT=8000
CHROMA_PERSIST_DIRECTORY=./rag-data/chroma-db

# OpenAI Embeddings
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=1536
```

### Dependências package.json:

```json
{
  "dependencies": {
    "chromadb": "^1.8.1",
    "openai": "^4.24.1"
  }
}
```

---

## 🎯 MÉTRICAS DE SUCESSO

### Benchmarks atuais (sem RAG):
- Precisão média: ~68%
- Tempo resposta: 22-40s
- Custo: $0.000270-$0.000435
- Context relevante: ~30%

### Metas com RAG:
- Precisão média: >85% (+25% melhoria)
- Tempo resposta: 25-45s (aceitável +5s)
- Custo: $0.000300-$0.000500 (+10% por embeddings)
- Context relevante: >80% (+50% melhoria)

### KPIs a medir:
1. **Precisão das respostas** (avaliação humana)
2. **Relevância dos documentos** recuperados
3. **Tempo de processamento** total
4. **Custo por consulta** (DeepSeek + embeddings)
5. **Taxa de fallback** (quando RAG falha)

---

## 🚨 RISCOS E MITIGAÇÕES

### Riscos identificados:
1. **Dependência de conectividade:** Chroma local pode falhar
2. **Custo de embeddings:** Pode aumentar gastos
3. **Latência adicional:** Busca vetorial adiciona tempo
4. **Qualidade de chunks:** Chunking ruim = resultados ruins

### Mitigações:
1. **Sistema de fallback:** Funciona sem RAG se necessário
2. **Cache agressivo:** Reutilizar embeddings existentes
3. **Otimização de queries:** Limitar top-k e threshold
4. **Iteração contínua:** Melhorar chunks baseado em feedback

---

## 📋 PRÓXIMOS PASSOS APÓS RAG

### FASE 2: RAG Avançado (Futuro)
1. **Migração para Pinecone** (produção)
2. **Hybrid search** (texto + semântico)
3. **RAG público** para munícipes
4. **Sistema de feedback** e aprendizado
5. **Expansão de conhecimento** baseada em uso

### FASE 3: IA Generativa Completa
1. **Geração de documentos** automática
2. **Workflow automation** via IA
3. **Predição de demandas** municipais
4. **Dashboard inteligente** para gestores

---

## 🎯 RESUMO PARA NOVA IA

**O QUE FAZER:**
1. Ler este documento completamente
2. Analisar arquivos de conhecimento em `/backend/knowledge/`
3. Implementar RAG conforme roadmap detalhado
4. Testar e comparar com sistema atual
5. Otimizar baseado em resultados

**ARQUIVOS IMPORTANTES:**
- `/backend/knowledge/municipal-knowledge.json` - Dados estruturados
- `/backend/src/services/deepSeekService.ts` - Serviço atual
- `/backend/src/scripts/test-improved-prompts.ts` - Testes de referência
- `prd_pv.md` - Contexto completo do projeto

**OBJETIVO FINAL:**
Sistema RAG funcionando que melhore precisão das respostas em 50-80%, mantendo performance aceitável e custos controlados.

---

**Data de criação:** 23/01/2025  
**Status:** Pronto para implementação  
**Próximo contexto:** Implementar FASE 1 do RAG (4-6 horas)