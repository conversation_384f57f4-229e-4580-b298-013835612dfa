// Script para testar API do backend diretamente
const https = require('https');
const http = require('http');

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    
    const req = client.request(url, options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsedBody });
        } catch {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testBackendAPI() {
  console.log('🧪 TESTANDO API DO BACKEND WINDOWS');
  console.log('==================================');
  
  const baseURL = 'http://localhost:3001';
  
  try {
    console.log('\n1. 🔍 Testando se backend está online...');
    
    try {
      const healthCheck = await makeRequest(`${baseURL}/health`, {
        method: 'GET',
        timeout: 5000
      });
      
      console.log('✅ Backend está online:', healthCheck.status);
      console.log('📊 Health data:', healthCheck.data);
    } catch (error) {
      console.log('❌ Backend não está respondendo:', error.message);
      console.log('🚨 PROBLEMA: Backend precisa estar rodando no Windows!');
      console.log('💡 Execute: cd backend && npm run dev');
      return;
    }
    
    console.log('\n2. 🔍 Testando endpoint de chat...');
    
    const chatData = {
      message: "Quantos protocolos temos em andamento?",
      secretaria: "administracao",
      userId: "test-user"
    };
    
    const chatResponse = await makeRequest(`${baseURL}/api/chat/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer fake-token-for-test'
      },
      timeout: 30000
    }, chatData);
    
    console.log('📊 Status da resposta:', chatResponse.status);
    
    if (chatResponse.status === 200) {
      console.log('✅ Resposta recebida com sucesso!');
      console.log('🤖 Conteúdo da resposta:');
      console.log('='.repeat(60));
      
      if (chatResponse.data.data && chatResponse.data.data.response) {
        const response = chatResponse.data.data.response;
        console.log(response);
        
        // Analisar se a resposta contém dados PostgreSQL
        if (response.includes('não tenho acesso ao banco de dados') || 
            response.includes('não tenho acesso direto ao banco')) {
          console.log('\n❌ PROBLEMA CONFIRMADO: Backend ainda responde sem acesso ao BD');
          console.log('🔧 A integração PostgreSQL NÃO está funcionando!');
        } else if (response.includes('protocolos') && 
                  (response.includes('26.760') || response.includes('26760'))) {
          console.log('\n✅ SUCESSO: Backend está usando dados PostgreSQL!');
          console.log('🎊 A integração está funcionando corretamente!');
        } else {
          console.log('\n⚠️ INCERTO: Resposta não contém padrões esperados');
          console.log('🔍 Pode estar usando dados mockados ou genéricos');
        }
      } else {
        console.log('\n⚠️ Formato de resposta inesperado:', chatResponse.data);
      }
      
      console.log('='.repeat(60));
      
      // Verificar metadados
      if (chatResponse.data.metadata) {
        console.log('\n📊 Metadados da resposta:');
        console.log('- Tempo de processamento:', chatResponse.data.metadata.processingTime, 'ms');
        console.log('- Tokens usados:', chatResponse.data.metadata.tokens?.total || 'N/A');
        console.log('- Custo:', chatResponse.data.data?.cost || 'N/A');
        console.log('- Cache hit:', chatResponse.data.cacheHit);
      }
      
    } else if (chatResponse.status === 401) {
      console.log('❌ Erro de autenticação (401)');
      console.log('🔧 Pode ser necessário token JWT válido');
      console.log('💡 Tente testar com usuário autenticado');
      
    } else if (chatResponse.status === 500) {
      console.log('❌ Erro interno do servidor (500)');
      console.log('🔧 Possíveis causas:');
      console.log('   - Erro na conexão PostgreSQL');
      console.log('   - Erro no deepSeekService');
      console.log('   - Problema de dependências');
      console.log('🔍 Verifique logs do backend Windows');
      
      if (chatResponse.data.error) {
        console.log('📝 Erro reportado:', chatResponse.data.error);
      }
      
    } else {
      console.log('⚠️ Status inesperado:', chatResponse.status);
      console.log('📝 Resposta:', chatResponse.data);
    }
    
    console.log('\n3. 🔍 Testando endpoint de PostgreSQL direto...');
    
    try {
      const pgResponse = await makeRequest(`${baseURL}/api/postgres/estatisticas`, {
        method: 'GET',
        timeout: 10000
      });
      
      if (pgResponse.status === 200) {
        console.log('✅ PostgreSQL endpoint funcionando!');
        console.log('📊 Estatísticas:', pgResponse.data.data);
      } else {
        console.log('❌ PostgreSQL endpoint falhou:', pgResponse.status);
      }
    } catch (error) {
      console.log('❌ PostgreSQL endpoint não disponível:', error.message);
    }
    
  } catch (error) {
    console.error('\n❌ ERRO GERAL NO TESTE:', error.message);
  }
  
  console.log('\n🎯 RESUMO DO DIAGNÓSTICO:');
  console.log('========================');
  console.log('1. Se backend não respondeu: Execute npm run dev no Windows');
  console.log('2. Se respondeu sem dados PostgreSQL: Problema na integração');
  console.log('3. Se erro 500: Verifique logs do console do backend');
  console.log('4. Se funcionou: Integração OK, problema pode ser no frontend');
}

testBackendAPI();