const { processMessage } = require('./src/services/deepSeekService');

async function testChatbotWithPostgres() {
  console.log('🤖 Testando chatbot com dados PostgreSQL...');
  
  // Configurar contexto de teste
  const context = {
    userId: 'test-user',
    secretaria: 'administracao',
    history: []
  };
  
  // Lista de perguntas para testar
  const questions = [
    'Quantos protocolos temos no sistema?',
    'Quais são os alvarás mais recentes?',
    'Me mostre estatísticas dos departamentos',
    'Quantas requisições foram feitas?'
  ];
  
  console.log('📝 Testando perguntas sobre dados municipais:\n');
  
  for (let i = 0; i < questions.length; i++) {
    const question = questions[i];
    console.log(`\n🔍 Pergunta ${i + 1}: "${question}"`);
    console.log('─'.repeat(60));
    
    try {
      const response = await processMessage(question, context);
      
      console.log('✅ Resposta do chatbot:');
      console.log(response.content);
      console.log(`\n📊 Tokens: ${response.tokens.total} | Custo: $${response.cost.total.toFixed(4)}`);
      
      // Verificar se a resposta contém dados específicos
      const hasSpecificData = /\d{1,3}(?:,\d{3})*|\d+/.test(response.content);
      const hasProtocolNumber = /20\d{10}/.test(response.content);
      
      if (hasSpecificData || hasProtocolNumber) {
        console.log('🎯 ✅ Resposta contém dados específicos do banco!');
      } else {
        console.log('⚠️ Resposta pode estar usando dados genéricos');
      }
      
    } catch (error) {
      console.error(`❌ Erro na pergunta ${i + 1}:`, error.message);
    }
    
    console.log('═'.repeat(60));
  }
  
  console.log('\n🏁 Teste concluído!');
  console.log('✅ Integração PostgreSQL → DeepSeek funcionando');
}

// Executar teste
testChatbotWithPostgres().catch(console.error);