import { Request, Response } from 'express';
import { CachedRequest } from '../middleware/cacheMiddleware';

interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  userId: string;
}

interface ConversationMessage {
  id: string;
  conversationId: string;
  userId: string;
  content: string;
  type: 'user' | 'assistant';
  timestamp: Date;
  metadata?: {
    responseTime?: number;
    tokensUsed?: number;
    cost?: number;
  };
}

// Mock storage - em produção seria MongoDB
let conversations: Conversation[] = [];
let messages: ConversationMessage[] = [];

class ConversationController {
  // Listar conversas do usuário
  static async getConversations(req: CachedRequest, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      
      if (!userId) {
        res.status(400).json({
          success: false,
          error: 'UserId obrigatório'
        });
        return;
      }

      const userConversations = conversations
        .filter(conv => conv.userId === userId)
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      res.json({
        success: true,
        data: userConversations
      });

    } catch (error) {
      console.error('Erro ao buscar conversas:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao buscar conversas'
      });
    }
  }

  // Criar nova conversa
  static async createConversation(req: CachedRequest, res: Response): Promise<void> {
    try {
      const { title, userId } = req.body;

      if (!userId) {
        res.status(400).json({
          success: false,
          error: 'UserId obrigatório'
        });
        return;
      }

      const newConversation: Conversation = {
        id: `conv_${Date.now()}`,
        title: title || 'Nova Conversa',
        lastMessage: '',
        timestamp: new Date(),
        messageCount: 0,
        userId
      };

      conversations.push(newConversation);

      res.json({
        success: true,
        data: newConversation
      });

    } catch (error) {
      console.error('Erro ao criar conversa:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao criar conversa'
      });
    }
  }

  // Obter mensagens da conversa
  static async getConversationMessages(req: CachedRequest, res: Response): Promise<void> {
    try {
      const { conversationId } = req.params;
      const { page = '1', limit = '50' } = req.query;

      if (!conversationId) {
        res.status(400).json({
          success: false,
          error: 'ConversationId obrigatório'
        });
        return;
      }

      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const offset = (pageNum - 1) * limitNum;

      const conversationMessages = messages
        .filter(msg => msg.conversationId === conversationId)
        .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
        .slice(offset, offset + limitNum);

      const totalMessages = messages.filter(msg => msg.conversationId === conversationId).length;

      res.json({
        success: true,
        data: {
          messages: conversationMessages,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: totalMessages,
            hasMore: offset + limitNum < totalMessages
          }
        }
      });

    } catch (error) {
      console.error('Erro ao buscar mensagens:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao buscar mensagens'
      });
    }
  }

  // Deletar conversa
  static async deleteConversation(req: CachedRequest, res: Response): Promise<void> {
    try {
      const { conversationId } = req.params;
      const { userId } = req.body;

      if (!conversationId || !userId) {
        res.status(400).json({
          success: false,
          error: 'ConversationId e userId obrigatórios'
        });
        return;
      }

      const conversationIndex = conversations.findIndex(
        conv => conv.id === conversationId && conv.userId === userId
      );

      if (conversationIndex === -1) {
        res.status(404).json({
          success: false,
          error: 'Conversa não encontrada'
        });
        return;
      }

      // Remover conversa e mensagens
      conversations.splice(conversationIndex, 1);
      messages = messages.filter(msg => msg.conversationId !== conversationId);

      res.json({
        success: true,
        message: 'Conversa deletada com sucesso'
      });

    } catch (error) {
      console.error('Erro ao deletar conversa:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao deletar conversa'
      });
    }
  }

  // Salvar mensagem na conversa
  static async saveMessage(
    conversationId: string, 
    userId: string, 
    content: string, 
    type: 'user' | 'assistant',
    metadata?: any
  ): Promise<ConversationMessage> {
    const message: ConversationMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      conversationId,
      userId,
      content,
      type,
      timestamp: new Date(),
      metadata
    };

    messages.push(message);

    // Atualizar conversa
    const conversation = conversations.find(conv => conv.id === conversationId);
    if (conversation) {
      conversation.lastMessage = content.substring(0, 100);
      conversation.messageCount = messages.filter(msg => msg.conversationId === conversationId).length;
      conversation.timestamp = new Date();
    }

    return message;
  }

  // Atualizar título da conversa
  static async updateConversationTitle(req: CachedRequest, res: Response): Promise<void> {
    try {
      const { conversationId } = req.params;
      const { title, userId } = req.body;

      if (!conversationId || !title || !userId) {
        res.status(400).json({
          success: false,
          error: 'ConversationId, title e userId obrigatórios'
        });
        return;
      }

      const conversation = conversations.find(
        conv => conv.id === conversationId && conv.userId === userId
      );

      if (!conversation) {
        res.status(404).json({
          success: false,
          error: 'Conversa não encontrada'
        });
        return;
      }

      conversation.title = title;

      res.json({
        success: true,
        data: conversation
      });

    } catch (error) {
      console.error('Erro ao atualizar título:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao atualizar título'
      });
    }
  }

  // Buscar conversas
  static async searchConversations(req: CachedRequest, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { q: query } = req.query;

      if (!userId || !query) {
        res.status(400).json({
          success: false,
          error: 'UserId e query obrigatórios'
        });
        return;
      }

      const searchTerm = (query as string).toLowerCase();
      const userConversations = conversations
        .filter(conv => 
          conv.userId === userId &&
          (conv.title.toLowerCase().includes(searchTerm) ||
           conv.lastMessage.toLowerCase().includes(searchTerm))
        )
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      res.json({
        success: true,
        data: userConversations
      });

    } catch (error) {
      console.error('Erro ao buscar conversas:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao buscar conversas'
      });
    }
  }
}

export default ConversationController;