// Script para testar integração deepSeekService no Windows
require('dotenv').config();

// Simular o deepSeekService sem DeepSeek API
const { Client } = require('pg');

class SimplePostgreSQLService {
  constructor() {
    this.config = {
      host: '*************',
      port: 5411,
      user: 'otto',
      password: 'otto',
      database: 'pv_valparaiso'
    };
  }

  async obterEstatisticas() {
    const client = new Client(this.config);
    try {
      await client.connect();
      const queries = await Promise.all([
        client.query('SELECT COUNT(*) as total FROM protocolo_virtual_processos'),
        client.query('SELECT COUNT(*) as total FROM requisicao'),
        client.query('SELECT COUNT(*) as total FROM servicos'),
        client.query('SELECT COUNT(*) as total FROM departamentos')
      ]);

      return {
        protocolos: parseInt(queries[0].rows[0].total),
        requisicoes: parseInt(queries[1].rows[0].total),
        servicos: parseInt(queries[2].rows[0].total),
        departamentos: parseInt(queries[3].rows[0].total)
      };
    } finally {
      await client.end();
    }
  }

  async buscarProtocolosComRequerente(termo, limite = 10) {
    const client = new Client(this.config);
    try {
      await client.connect();
      const query = `
        SELECT DISTINCT
          p.id,
          p.id_protocolo,
          p.data_protocolo::text,
          COALESCE(p.requerente, 'Não informado') as requerente,
          COALESCE(a.descricao, 'Não informado') as assunto,
          COALESCE(s.descricao, 'Não informado') as situacao,
          COALESCE(d.descricao, 'Não informado') as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE ($1 IS NULL OR 
          LOWER(a.descricao) LIKE LOWER($1) OR 
          LOWER(s.descricao) LIKE LOWER($1) OR
          LOWER(p.id_protocolo) LIKE LOWER($1) OR
          LOWER(p.requerente) LIKE LOWER($1))
        ORDER BY p.data_protocolo DESC
        LIMIT $2
      `;
      
      const result = await client.query(query, [
        termo ? `%${termo}%` : null, 
        limite
      ]);
      
      return result.rows;
    } finally {
      await client.end();
    }
  }

  async buscarAlvaras(limite = 10) {
    const client = new Client(this.config);
    try {
      await client.connect();
      const query = `
        SELECT 
          p.id,
          p.id_protocolo,
          p.data_protocolo::text,
          a.descricao as assunto,
          s.descricao as situacao,
          d.descricao as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE LOWER(a.descricao) LIKE '%alvará%' 
           OR LOWER(a.descricao) LIKE '%licença%'
           OR LOWER(a.descricao) LIKE '%funcionamento%'
        ORDER BY p.data_protocolo DESC
        LIMIT $1
      `;
      
      const result = await client.query(query, [limite]);
      return result.rows;
    } finally {
      await client.end();
    }
  }
}

// Simular processMessage
async function testDeepSeekIntegration() {
  console.log('🧪 TESTANDO INTEGRAÇÃO DEEPSEEK + POSTGRESQL');
  console.log('==============================================');
  
  const message = "Quantos protocolos temos em andamento?";
  const context = {
    userId: 'test-user',
    secretaria: 'administracao'
  };
  
  console.log('📝 Mensagem:', message);
  console.log('🏢 Secretaria:', context.secretaria);
  
  try {
    console.log('\n🔍 1. TESTANDO DETECÇÃO DE INTENÇÃO...');
    const messageQuery = message.toLowerCase();
    const isProtocolQuery = messageQuery.includes('protocolo') || 
                           messageQuery.includes('processo') ||
                           messageQuery.includes('alvará') ||
                           messageQuery.includes('licença') ||
                           messageQuery.includes('licenca') ||
                           messageQuery.includes('andamento') ||
                           messageQuery.includes('situação') ||
                           messageQuery.includes('situacao');
    
    console.log('✅ isProtocolQuery:', isProtocolQuery);
    
    if (!isProtocolQuery) {
      console.log('❌ PROBLEMA: Detecção de intenção falhou!');
      return;
    }
    
    console.log('\n🔍 2. TESTANDO CONEXÃO POSTGRESQL...');
    const postgresService = new SimplePostgreSQLService();
    
    const [estatisticas, protocolosRecentes, alvaras] = await Promise.all([
      postgresService.obterEstatisticas(),
      postgresService.buscarProtocolosComRequerente(
        messageQuery.includes('alvará') || messageQuery.includes('alvara') ? 'alvará' : 
        messageQuery.includes('licença') || messageQuery.includes('licenca') ? 'licença' : undefined, 
        5
      ),
      postgresService.buscarAlvaras(5)
    ]);
    
    console.log('✅ Estatísticas obtidas:', estatisticas);
    console.log('✅ Protocolos recentes:', protocolosRecentes.length);
    console.log('✅ Alvarás encontrados:', alvaras.length);
    
    const dadosRelevantes = { 
      estatisticas, 
      protocolosRecentes, 
      alvaras,
      tipoConsulta: 'protocolos'
    };
    
    console.log('\n🔍 3. CONSTRUINDO PROMPT DO SISTEMA...');
    
    let systemPrompt = 'Você é um assistente virtual da Prefeitura de Valparaíso de Goiás.';
    
    const dataAtual = new Date().toLocaleDateString('pt-BR');
    systemPrompt += `\n\n**DADOS MUNICIPAIS ATUALIZADOS EM TEMPO REAL (${dataAtual}):**\n`;
    systemPrompt += `- 📊 Total de protocolos no sistema: ${dadosRelevantes.estatisticas.protocolos.toLocaleString('pt-BR')}\n`;
    systemPrompt += `- 📋 Total de requisições: ${dadosRelevantes.estatisticas.requisicoes.toLocaleString('pt-BR')}\n`;
    systemPrompt += `- 🏢 Serviços municipais ativos: ${dadosRelevantes.estatisticas.servicos}\n`;
    systemPrompt += `- 🏛️ Departamentos ativos: ${dadosRelevantes.estatisticas.departamentos}\n\n`;
    
    if (dadosRelevantes.tipoConsulta === 'protocolos' && dadosRelevantes.protocolosRecentes.length > 0) {
      systemPrompt += `**PROTOCOLOS RECENTES ENCONTRADOS:**\n`;
      dadosRelevantes.protocolosRecentes.forEach((p, index) => {
        systemPrompt += `${index + 1}. Protocolo ${p.id_protocolo}:\n`;
        systemPrompt += `   - Assunto: ${p.assunto}\n`;
        systemPrompt += `   - Situação: ${p.situacao}\n`;
        systemPrompt += `   - Data: ${new Date(p.data_protocolo).toLocaleDateString('pt-BR')}\n`;
        if (p.requerente && p.requerente !== 'Não informado') {
          systemPrompt += `   - Requerente: ${p.requerente}\n`;
        }
        systemPrompt += `\n`;
      });
    }
    
    if (dadosRelevantes.alvaras && dadosRelevantes.alvaras.length > 0) {
      systemPrompt += `**ALVARÁS E LICENÇAS RECENTES:**\n`;
      dadosRelevantes.alvaras.forEach((a, index) => {
        systemPrompt += `${index + 1}. Protocolo ${a.id_protocolo}:\n`;
        systemPrompt += `   - Tipo: ${a.assunto}\n`;
        systemPrompt += `   - Status: ${a.situacao}\n`;
        systemPrompt += `   - Departamento: ${a.departamento}\n\n`;
      });
    }
    
    systemPrompt += `**INSTRUÇÕES PARA USO DOS DADOS REAIS:**\n`;
    systemPrompt += `- SEMPRE use estes dados atualizados em suas respostas\n`;
    systemPrompt += `- Seja específico e preciso com números e datas\n`;
    systemPrompt += `- Cite protocolos específicos quando relevante\n`;
    systemPrompt += `- Nunca diga "não tenho acesso ao banco de dados" - você TEM acesso aos dados acima\n`;
    systemPrompt += `- Use os números reais para dar respostas concretas e úteis\n\n`;
    
    console.log('✅ Prompt construído com sucesso!');
    
    console.log('\n🎯 PROMPT COMPLETO PARA DEEPSEEK:');
    console.log('='.repeat(80));
    console.log(systemPrompt);
    console.log('='.repeat(80));
    
    // Simular resposta baseada no prompt
    const simulatedResponse = `Baseado nos dados atualizados do sistema municipal:

📊 **Protocolos Municipais Atuais:**
- Total de protocolos no sistema: ${dadosRelevantes.estatisticas.protocolos.toLocaleString('pt-BR')}
- Requisições processadas: ${dadosRelevantes.estatisticas.requisicoes.toLocaleString('pt-BR')}

📋 **Protocolos em Andamento:**
${dadosRelevantes.protocolosRecentes.length > 0 ? 
  dadosRelevantes.protocolosRecentes.map((p, idx) => 
    `${idx + 1}. Protocolo ${p.id_protocolo} - ${p.assunto} (${p.situacao})`
  ).join('\n') : 
  'Consultando protocolos específicos em andamento...'}

*Dados consultados em tempo real do sistema PostgreSQL em ${new Date().toLocaleString('pt-BR')}*`;
    
    console.log('\n🤖 RESPOSTA SIMULADA:');
    console.log('='.repeat(80));
    console.log(simulatedResponse);
    console.log('='.repeat(80));
    
    console.log('\n✅ TESTE COMPLETO REALIZADO COM SUCESSO!');
    console.log('🔥 A INTEGRAÇÃO DEVERIA ESTAR FUNCIONANDO!');
    
    console.log('\n🚨 PRÓXIMOS PASSOS PARA DEBUG:');
    console.log('1. Verificar logs do backend Windows');
    console.log('2. Confirmar se deepSeekService.ts está sendo compilado');
    console.log('3. Verificar se há erros na DeepSeek API');
    console.log('4. Testar endpoint /api/chat/message diretamente');
    
  } catch (error) {
    console.error('\n❌ ERRO NO TESTE:', error.message);
    console.error('🔧 Stack:', error.stack);
  }
}

testDeepSeekIntegration();