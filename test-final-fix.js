// TESTE FINAL APÓS CORREÇÃO COMPLETA DAS QUERIES SQL
const http = require('http');

console.log('🎯 TESTE FINAL - CORREÇÃO COMPLETA APLICADA');
console.log('==========================================');
console.log('✅ Correção 1: Removido campo "requerente" inexistente');
console.log('✅ Correção 2: Corrigido LOWER() em campo bigint (id_protocolo)');
console.log('');

async function testFinalFix() {
  console.log('⏳ Aguardando 3 segundos para tsx recompilar...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Usar timestamp único para evitar cache
  const timestamp = Date.now();
  const uniqueMessage = `Quantos protocolos em andamento temos hoje final ${timestamp}?`;
  
  console.log('📝 Pergunta final:', uniqueMessage);
  console.log('🚀 Enviando requisição...');
  
  try {
    const response = await makeRequest('http://localhost:3001/api/chat/message', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    }, {
      message: uniqueMessage,
      secretaria: "administracao", 
      userId: "test-final-" + timestamp
    });
    
    console.log('\n📊 RESULTADO FINAL:');
    console.log('==================');
    console.log('Status HTTP:', response.status);
    
    if (response.data.success) {
      const chatResponse = response.data.data.response;
      const cacheHit = response.data.cacheHit;
      
      console.log('Cache Hit:', cacheHit);
      console.log('Processamento:', response.data.metadata?.processingTime || 'N/A', 'ms');
      
      console.log('\n🤖 RESPOSTA FINAL DO CHATBOT:');
      console.log('='.repeat(80));
      console.log(chatResponse);
      console.log('='.repeat(80));
      
      // Análise final da resposta
      if (chatResponse.includes('não tenho acesso ao banco de dados') || 
          chatResponse.includes('não tenho acesso direto ao banco') ||
          chatResponse.includes('não tenho acesso em tempo real')) {
        console.log('\n❌ FALHA: AINDA SEM INTEGRAÇÃO POSTGRESQL');
        console.log('🔧 Problemas possíveis:');
        console.log('   - Outros erros SQL não identificados');
        console.log('   - Problema na DeepSeek API');
        console.log('   - Erro na lógica de integração');
        console.log('💡 Verificar logs detalhados do backend');
        
      } else if (chatResponse.includes('26.760') || chatResponse.includes('26760') ||
                chatResponse.includes('13.697') || chatResponse.includes('13697') ||
                chatResponse.includes('protocolos no sistema') || 
                chatResponse.includes('consultando nosso sistema') ||
                chatResponse.includes('dados atualizados') ||
                chatResponse.includes('tempo real')) {
        console.log('\n🎊 SUCESSO ABSOLUTO! INTEGRAÇÃO POSTGRESQL 100% FUNCIONANDO!');
        console.log('=====================================');
        console.log('✅ Correções SQL resolveram todos os problemas');
        console.log('✅ Chatbot agora acessa dados reais do PostgreSQL');
        console.log('✅ Integração completa e operacional');
        console.log('✅ Fim das respostas "não tenho acesso ao banco"');
        console.log('');
        console.log('🏆 MISSÃO CUMPRIDA: CHATBOT COM DADOS REAIS ATIVO!');
        
      } else {
        console.log('\n⚠️ RESULTADO PARCIAL');
        console.log('🔍 Resposta não contém padrões "não tenho acesso"');
        console.log('🔍 Mas também não contém dados específicos esperados');
        console.log('💡 Pode estar funcionando com dados diferentes');
        console.log('💡 Analisar resposta manualmente para confirmar');
      }
      
      // Metadados adicionais
      console.log('\n📊 DETALHES TÉCNICOS:');
      console.log('====================');
      console.log('- Tokens utilizados:', response.data.metadata?.tokens?.total || 'N/A');
      console.log('- Custo da operação:', response.data.data?.cost || 'N/A');
      console.log('- Fonte dos dados:', response.data.data?.source || 'N/A');
      console.log('- Timestamp:', new Date().toLocaleString('pt-BR'));
      
    } else {
      console.log('\n❌ ERRO NA API:');
      console.log('===============');
      console.log('Erro:', response.data.error);
      console.log('Detalhes:', response.data);
    }
    
  } catch (error) {
    console.error('\n❌ ERRO NA REQUISIÇÃO:', error.message);
    console.error('🔧 Possíveis causas:');
    console.error('   - Backend não está respondendo');
    console.error('   - Timeout devido a processamento longo');
    console.error('   - Erro interno no servidor');
  }
  
  console.log('\n🎯 RESUMO FINAL:');
  console.log('================');
  console.log('Todas as correções SQL foram aplicadas.');
  console.log('Se ainda não funcionar, o problema está em outro lugar.');
  console.log('Verifique logs do backend para erros adicionais.');
}

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const reqOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.pathname,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 25000  // Timeout maior para processamento
    };
    
    const req = http.request(reqOptions, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsedBody });
        } catch {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout - processo pode estar demorando'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

testFinalFix();