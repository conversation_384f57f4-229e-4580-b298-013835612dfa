'use client';

import { useState } from 'react';
import { Logo } from '@/components/ui';
import { ChatWindow, ConversationList, MessageInput } from '@/components/chat';
import { useChat } from '@/hooks/useChat';

// Mock data para demonstração
const mockConversations = [
  {
    id: '1',
    title: 'Consulta sobre funcionários da saúde',
    lastMessage: 'A Secretaria de Saúde possui 45 funcionários ativos...',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 horas atrás
    messageCount: 6
  },
  {
    id: '2', 
    title: 'Relatório de departamentos',
    lastMessage: 'Encontrei 12 departamentos ativos no sistema...',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 dia atrás
    messageCount: 4
  },
  {
    id: '3',
    title: 'Informações sobre licenças',
    lastMessage: 'Para solicitar uma licença, você precisa...',
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 dias atrás
    messageCount: 8
  }
];

export default function ChatPage() {
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [conversations] = useState(mockConversations);
  
  const { messages, isLoading, sendMessage, clearMessages } = useChat();

  const handleSendMessage = async (message: string) => {
    try {
      await sendMessage({ message, conversationId: activeConversationId || undefined });
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  const handleNewConversation = () => {
    clearMessages();
    setActiveConversationId(null);
  };

  const handleSelectConversation = (conversationId: string) => {
    // TODO: Carregar mensagens da conversa selecionada
    setActiveConversationId(conversationId);
    console.log('Carregar conversa:', conversationId);
  };

  const handleDeleteConversation = (conversationId: string) => {
    // TODO: Implementar deleção de conversa
    console.log('Deletar conversa:', conversationId);
  };

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* Header */}
      <header className="bg-gradient-to-r from-pv-blue-primary to-pv-blue-secondary text-white shadow-lg">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Logo variant="horizontal" size="sm" />
              <div>
                <h1 className="text-xl font-semibold">Chatbot Inteligente</h1>
                <p className="text-pv-blue-100 text-sm">Prefeitura de Valparaíso de Goiás</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Status Indicator */}
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-pv-blue-100">Sistema Online</span>
              </div>
              
              {/* Help Button */}
              <button className="p-2 hover:bg-pv-blue-600 rounded-lg transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar - Conversation List - Hidden on mobile by default */}
        <div className="hidden lg:block">
          <ConversationList
          conversations={conversations}
          activeConversationId={activeConversationId}
          onSelectConversation={handleSelectConversation}
          onNewConversation={handleNewConversation}
          onDeleteConversation={handleDeleteConversation}
          />
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Chat Header */}
          <div className="border-b border-pv-gray-200 bg-white px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-pv-gray-800">
                  {activeConversationId 
                    ? conversations.find(c => c.id === activeConversationId)?.title || 'Conversa'
                    : 'Nova Conversa'
                  }
                </h2>
                <p className="text-sm text-pv-gray-600">
                  Consulte informações municipais, departamentos e serviços
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                {/* Export Button */}
                <button className="p-2 text-pv-gray-500 hover:text-pv-blue-primary hover:bg-pv-blue-50 rounded-lg transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </button>
                
                {/* Clear Chat Button */}
                <button 
                  onClick={handleNewConversation}
                  className="p-2 text-pv-gray-500 hover:text-pv-blue-primary hover:bg-pv-blue-50 rounded-lg transition-colors"
                  title="Limpar conversa"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Chat Messages */}
          <ChatWindow messages={messages} isLoading={isLoading} />

          {/* Message Input */}
          <MessageInput 
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            placeholder="Faça uma pergunta sobre serviços municipais, departamentos ou funcionários..."
          />
        </div>
      </div>

      {/* Footer */}
      <footer className="border-t border-pv-gray-200 bg-pv-gray-50 px-6 py-3">
        <div className="flex items-center justify-between text-sm text-pv-gray-600">
          <div className="flex items-center space-x-4">
            <span>© 2025 Prefeitura de Valparaíso de Goiás</span>
            <span>•</span>
            <span>Sistema de IA Municipal</span>
          </div>
          
      
        </div>
      </footer>
    </div>
  );
}