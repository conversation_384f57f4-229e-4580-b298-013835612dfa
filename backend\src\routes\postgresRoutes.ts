import { Router } from 'express';
import { PostgresController } from '../controllers/postgresController';

const router = Router();

// Rotas para consultas de protocolos
router.get('/protocolos/buscar', PostgresController.buscarProtocolos);
router.get('/protocolos/alvara', PostgresController.buscarAlvaras);
router.get('/protocolos/situacao', PostgresController.buscarProtocolosPorSituacao);
router.get('/protocolos/:numero', PostgresController.buscarProtocoloPorNumero);

// Rotas para serviços municipais
router.get('/servicos', PostgresController.listarServicos);

// Rotas para estatísticas e dashboard
router.get('/estatisticas', PostgresController.obterEstatisticas);
router.get('/dashboard', PostgresController.dashboard);

// Rota para teste de conexão
router.get('/health', PostgresController.testarConexao);

export default router;