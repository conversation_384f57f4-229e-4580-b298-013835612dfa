// Teste com pergunta super clara para ativar PostgreSQL
const http = require('http');

console.log('🎯 TESTE COM PERGUNTA SUPER CLARA');
console.log('=================================');

async function testClearQuestion() {
  const clearQuestions = [
    "Quantos protocolos existem no total?",
    "Dados dos protocolos municipais", 
    "Estatísticas dos protocolos",
    "Total de protocolos registrados",
    "Quantos protocolos em andamento?"
  ];
  
  for (let i = 0; i < clearQuestions.length; i++) {
    const question = clearQuestions[i];
    
    console.log(`\n${i + 1}. 🧪 TESTE: "${question}"`);
    console.log('='.repeat(50));
    console.log('🔍 Verificando se esta pergunta ativa PostgreSQL...');
    
    try {
      const response = await makeRequest('http://localhost:3001/api/chat/message', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      }, {
        message: question,
        secretaria: "administracao", 
        userId: "test-clear-" + Date.now() + i
      });
      
      if (response.data.success) {
        const chatResponse = response.data.data.response;
        
        console.log('✅ Resposta recebida');
        console.log('Cache Hit:', response.data.cacheHit);
        console.log('Tempo:', response.data.metadata?.processingTime || 'N/A', 'ms');
        
        // Verificar se contém dados específicos
        if (chatResponse.includes('26.760') || chatResponse.includes('26760') ||
            chatResponse.includes('13.697') || chatResponse.includes('13697') ||
            chatResponse.includes('protocolos no sistema') ||
            chatResponse.includes('consultando nosso sistema') ||
            chatResponse.includes('dados atualizados') ||
            chatResponse.includes('tempo real') ||
            chatResponse.includes('PostgreSQL')) {
          
          console.log('🎊 SUCESSO! ESTA PERGUNTA ATIVOU POSTGRESQL!');
          console.log('='.repeat(60));
          console.log(chatResponse);
          console.log('='.repeat(60));
          console.log('✅ INTEGRAÇÃO POSTGRESQL FUNCIONANDO!');
          return; // Parar no primeiro sucesso
          
        } else if (chatResponse.includes('não disponibiliza') ||
                   chatResponse.includes('não tenho acesso') ||
                   chatResponse.includes('entre em contato')) {
          console.log('❌ Ainda resposta genérica');
          
        } else {
          console.log('⚠️ Resposta diferente:', chatResponse.substring(0, 100) + '...');
        }
        
      } else {
        console.log('❌ Erro API:', response.data.error);
      }
      
      // Pausa entre testes
      if (i < clearQuestions.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
    } catch (error) {
      console.log('❌ Erro requisição:', error.message);
    }
  }
  
  console.log('\n🔥 SE NENHUMA PERGUNTA FUNCIONOU:');
  console.log('=================================');
  console.log('O problema é que a detecção NÃO está capturando essas palavras.');
  console.log('Vamos verificar o código de detecção diretamente.');
  console.log('');
  console.log('🚨 ÚLTIMA TENTATIVA - FORÇAR DETECÇÃO MAIS AMPLA!');
}

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const reqOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.pathname,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 20000
    };
    
    const req = http.request(reqOptions, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsedBody });
        } catch {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

testClearQuestion();