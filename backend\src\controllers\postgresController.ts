import { Request, Response } from 'express';
import { PostgreSQLQueryService } from '../services/PostgreSQLQueryService';

const postgresService = new PostgreSQLQueryService();

export class PostgresController {
  /**
   * Buscar protocolos com filtros
   */
  static async buscarProtocolos(req: Request, res: Response) {
    try {
      const { termo, limite } = req.query;
      
      console.log(`🔍 Buscando protocolos: termo="${termo}", limite="${limite}"`);
      
      const protocolos = await postgresService.buscarProtocolos(
        termo as string, 
        parseInt(limite as string) || 10
      );
      
      res.json({ 
        success: true, 
        data: protocolos,
        count: protocolos.length,
        timestamp: new Date().toISOString()
      });
      
    } catch (error: any) {
      console.error('❌ Erro ao buscar protocolos:', error);
      res.status(500).json({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Obter estatísticas gerais do sistema
   */
  static async obterEstatisticas(req: Request, res: Response) {
    try {
      console.log('📊 Obtendo estatísticas gerais...');
      
      const [stats, statsPorDepartamento] = await Promise.all([
        postgresService.obterEstatisticas(),
        postgresService.obterEstatisticasPorDepartamento()
      ]);
      
      res.json({ 
        success: true, 
        data: {
          geral: stats,
          porDepartamento: statsPorDepartamento.slice(0, 10), // Top 10
          resumo: {
            totalProtocolos: stats.protocolos,
            totalRequisicoes: stats.requisicoes,
            servicosAtivos: stats.servicos,
            departamentosAtivos: stats.departamentos
          }
        },
        timestamp: new Date().toISOString()
      });
      
    } catch (error: any) {
      console.error('❌ Erro ao obter estatísticas:', error);
      res.status(500).json({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Buscar protocolo específico por número
   */
  static async buscarProtocoloPorNumero(req: Request, res: Response) {
    try {
      const { numero } = req.params;
      
      console.log(`🔍 Buscando protocolo: ${numero}`);
      
      const protocolo = await postgresService.buscarProtocoloPorNumero(numero);
      
      if (protocolo) {
        res.json({ 
          success: true, 
          data: protocolo,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(404).json({ 
          success: false, 
          error: 'Protocolo não encontrado',
          numeroConsultado: numero,
          timestamp: new Date().toISOString()
        });
      }
      
    } catch (error: any) {
      console.error('❌ Erro ao buscar protocolo por número:', error);
      res.status(500).json({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Buscar alvarás especificamente
   */
  static async buscarAlvaras(req: Request, res: Response) {
    try {
      const { limite } = req.query;
      
      console.log(`🔍 Buscando alvarás, limite: ${limite}`);
      
      const alvaras = await postgresService.buscarAlvaras(
        parseInt(limite as string) || 10
      );
      
      res.json({ 
        success: true, 
        data: alvaras,
        count: alvaras.length,
        timestamp: new Date().toISOString()
      });
      
    } catch (error: any) {
      console.error('❌ Erro ao buscar alvarás:', error);
      res.status(500).json({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Buscar protocolos por situação
   */
  static async buscarProtocolosPorSituacao(req: Request, res: Response) {
    try {
      const { situacao, limite } = req.query;
      
      if (!situacao) {
        return res.status(400).json({
          success: false,
          error: 'Parâmetro "situacao" é obrigatório',
          timestamp: new Date().toISOString()
        });
      }
      
      console.log(`🔍 Buscando protocolos por situação: ${situacao}`);
      
      const protocolos = await postgresService.buscarProtocolosPorSituacao(
        situacao as string,
        parseInt(limite as string) || 20
      );
      
      res.json({ 
        success: true, 
        data: protocolos,
        count: protocolos.length,
        situacaoConsultada: situacao,
        timestamp: new Date().toISOString()
      });
      
    } catch (error: any) {
      console.error('❌ Erro ao buscar protocolos por situação:', error);
      res.status(500).json({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Listar serviços municipais
   */
  static async listarServicos(req: Request, res: Response) {
    try {
      const { departamentoId } = req.query;
      
      console.log(`🔍 Listando serviços, departamento: ${departamentoId}`);
      
      const servicos = await postgresService.listarServicos(
        departamentoId ? parseInt(departamentoId as string) : undefined
      );
      
      res.json({ 
        success: true, 
        data: servicos,
        count: servicos.length,
        departamentoFiltrado: departamentoId || null,
        timestamp: new Date().toISOString()
      });
      
    } catch (error: any) {
      console.error('❌ Erro ao listar serviços:', error);
      res.status(500).json({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Dashboard de métricas resumidas
   */
  static async dashboard(req: Request, res: Response) {
    try {
      console.log('📊 Obtendo dashboard completo...');
      
      const [
        estatisticas,
        estatDepartamentos,
        protocolosRecentes,
        alvarasRecentes
      ] = await Promise.all([
        postgresService.obterEstatisticas(),
        postgresService.obterEstatisticasPorDepartamento(),
        postgresService.buscarProtocolos(undefined, 5),
        postgresService.buscarAlvaras(5)
      ]);
      
      // Calcular métricas adicionais
      const protocolosAndamento = estatDepartamentos.reduce((acc, dept) => acc + parseInt(dept.em_andamento), 0);
      const protocolosConcluidos = estatDepartamentos.reduce((acc, dept) => acc + parseInt(dept.concluidos), 0);
      
      const dashboard = {
        resumo: {
          totalProtocolos: estatisticas.protocolos,
          totalRequisicoes: estatisticas.requisicoes,
          servicosAtivos: estatisticas.servicos,
          departamentosAtivos: estatisticas.departamentos,
          protocolosAndamento,
          protocolosConcluidos,
          taxaConclusao: estatisticas.protocolos > 0 
            ? Math.round((protocolosConcluidos / estatisticas.protocolos) * 100) 
            : 0
        },
        protocolosRecentes,
        alvarasRecentes,
        topDepartamentos: estatDepartamentos.slice(0, 5),
        ultimaAtualizacao: new Date().toISOString()
      };
      
      res.json({ 
        success: true, 
        data: dashboard,
        timestamp: new Date().toISOString()
      });
      
    } catch (error: any) {
      console.error('❌ Erro ao obter dashboard:', error);
      res.status(500).json({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Testar conexão com PostgreSQL
   */
  static async testarConexao(req: Request, res: Response) {
    try {
      console.log('🔗 Testando conexão PostgreSQL...');
      
      const isConnected = await postgresService.testarConexao();
      
      if (isConnected) {
        const stats = await postgresService.obterEstatisticas();
        
        res.json({ 
          success: true, 
          message: 'Conexão PostgreSQL funcionando',
          data: {
            status: 'CONECTADO',
            host: '*************',
            port: 5411,
            database: 'pv_valparaiso',
            estatisticasRapidas: stats
          },
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(503).json({ 
          success: false, 
          message: 'Falha na conexão PostgreSQL',
          data: {
            status: 'DESCONECTADO',
            host: '*************',
            port: 5411,
            database: 'pv_valparaiso'
          },
          timestamp: new Date().toISOString()
        });
      }
      
    } catch (error: any) {
      console.error('❌ Erro ao testar conexão:', error);
      res.status(500).json({ 
        success: false, 
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
}