const { PostgreSQLQueryService } = require('./src/services/PostgreSQLQueryService');

async function testCompletePostgresIntegration() {
  console.log('🔬 Teste completo da integração PostgreSQL');
  console.log('═'.repeat(50));
  
  const service = new PostgreSQLQueryService();
  
  try {
    // Teste 1: Estatísticas gerais
    console.log('\n📊 Teste 1: Estatísticas gerais');
    console.log('─'.repeat(30));
    const stats = await service.obterEstatisticas();
    console.log(`✅ Protocolos: ${stats.protocolos.toLocaleString('pt-BR')}`);
    console.log(`✅ Requisições: ${stats.requisicoes.toLocaleString('pt-BR')}`);
    console.log(`✅ Serviços: ${stats.servicos}`);
    console.log(`✅ Departamentos: ${stats.departamentos}`);
    
    // Teste 2: Buscar protocolos com termo "alvará"
    console.log('\n🏛️ Teste 2: Buscar protocolos (termo: "alvará")');
    console.log('─'.repeat(40));
    const protocolos = await service.buscarProtocolos('alvará', 5);
    console.log(`✅ Encontrados ${protocolos.length} protocolos:`);
    protocolos.forEach((p, index) => {
      console.log(`  ${index + 1}. ${p.id_protocolo} - ${p.assunto} (${p.situacao})`);
    });
    
    // Teste 3: Buscar alvarás especificamente
    console.log('\n🏢 Teste 3: Buscar alvarás específicos');
    console.log('─'.repeat(35));
    const alvaras = await service.buscarAlvaras(5);
    console.log(`✅ Encontrados ${alvaras.length} alvarás:`);
    alvaras.forEach((a, index) => {
      console.log(`  ${index + 1}. ${a.id_protocolo} - ${a.assunto} (${a.situacao})`);
    });
    
    // Teste 4: Buscar protocolo específico
    if (protocolos.length > 0) {
      const numeroTeste = protocolos[0].id_protocolo;
      console.log(`\n🔍 Teste 4: Buscar protocolo específico (${numeroTeste})`);
      console.log('─'.repeat(45));
      const protocolo = await service.buscarProtocoloPorNumero(numeroTeste);
      if (protocolo) {
        console.log(`✅ Protocolo encontrado:`);
        console.log(`  - Número: ${protocolo.id_protocolo}`);
        console.log(`  - Assunto: ${protocolo.assunto}`);
        console.log(`  - Situação: ${protocolo.situacao}`);
        console.log(`  - Requerente: ${protocolo.requerente}`);
        console.log(`  - Data: ${new Date(protocolo.data_protocolo).toLocaleDateString('pt-BR')}`);
      }
    }
    
    // Teste 5: Estatísticas por departamento
    console.log('\n🏛️ Teste 5: Estatísticas por departamento');
    console.log('─'.repeat(40));
    const estatDept = await service.obterEstatisticasPorDepartamento();
    console.log(`✅ Top 5 departamentos com mais protocolos:`);
    estatDept.slice(0, 5).forEach((dept, index) => {
      console.log(`  ${index + 1}. ${dept.departamento}:`);
      console.log(`     Total: ${dept.total_protocolos}, Andamento: ${dept.em_andamento}, Concluídos: ${dept.concluidos}`);
    });
    
    // Teste 6: Listar serviços
    console.log('\n🔧 Teste 6: Listar serviços municipais');
    console.log('─'.repeat(35));
    const servicos = await service.listarServicos();
    console.log(`✅ Encontrados ${servicos.length} serviços. Primeiros 10:`);
    servicos.slice(0, 10).forEach((s, index) => {
      console.log(`  ${index + 1}. ${s.descricao}${s.departamento ? ` (${s.departamento})` : ''}`);
    });
    
    // Teste 7: Teste de conexão
    console.log('\n🔗 Teste 7: Teste de conexão');
    console.log('─'.repeat(25));
    const isConnected = await service.testarConexao();
    console.log(`✅ Status da conexão: ${isConnected ? 'CONECTADO' : 'DESCONECTADO'}`);
    
    console.log('\n🎉 RESUMO DO TESTE');
    console.log('═'.repeat(50));
    console.log('✅ Todas as funcionalidades PostgreSQL estão funcionando!');
    console.log('✅ O chatbot agora tem acesso aos dados reais:');
    console.log(`   - ${stats.protocolos.toLocaleString('pt-BR')} protocolos municipais`);
    console.log(`   - ${stats.requisicoes.toLocaleString('pt-BR')} requisições`);
    console.log(`   - ${stats.servicos} serviços disponíveis`);
    console.log(`   - ${stats.departamentos} departamentos ativos`);
    console.log('🎯 OBJETIVO ALCANÇADO: Integração PostgreSQL completa!');
    
  } catch (error) {
    console.error('❌ Erro no teste completo:', error.message);
  } finally {
    await service.fecharConexoes();
    console.log('\n🔌 Conexões PostgreSQL fechadas');
  }
}

testCompletePostgresIntegration();