// Teste para verificar por que a integração PostgreSQL não funciona no servidor

const { Client } = require('pg');

// Função para adicionar logs detalhados ao deepSeekService
console.log('🔧 CRIANDO VERSÃO DE DEBUG DO DEEPSEEK SERVICE...\n');

// Simular exatamente o que acontece no processMessage
async function debugProcessMessage(message, context) {
  console.log(`🤖 [DEBUG] Processando mensagem: "${message}"`);
  console.log(`📍 [DEBUG] Secretaria: ${context.secretaria}`);
  
  // **INTEGRAÇÃO POSTGRESQL**: Buscar dados reais baseados na mensagem
  const postgresService = {
    async obterEstatisticas() {
      const client = new Client({
        host: '*************',
        port: 5411,
        user: 'otto',
        password: 'otto',
        database: 'pv_valparaiso'
      });
      
      await client.connect();
      const result = await client.query('SELECT COUNT(*) as total FROM protocolo_virtual_processos');
      await client.end();
      
      return {
        protocolos: parseInt(result.rows[0].total),
        requisicoes: 339475,
        servicos: 145,
        departamentos: 500
      };
    },
    
    async buscarProtocolos(termo, limite) {
      const client = new Client({
        host: '*************',
        port: 5411,
        user: 'otto',
        password: 'otto',
        database: 'pv_valparaiso'
      });
      
      await client.connect();
      
      const query = `
        SELECT p.id_protocolo, p.data_protocolo, 
               COALESCE(a.descricao, 'Não informado') as assunto,
               COALESCE(s.descricao, 'Não informado') as situacao,
               COALESCE(p.requerente, 'Não informado') as requerente
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        WHERE ($1 IS NULL OR LOWER(a.descricao) LIKE LOWER($1))
        ORDER BY p.data_protocolo DESC
        LIMIT $2
      `;
      
      const result = await client.query(query, [
        termo ? `%${termo}%` : null, 
        limite
      ]);
      
      await client.end();
      return result.rows;
    },
    
    async buscarAlvaras(limite) {
      const client = new Client({
        host: '*************',
        port: 5411,
        user: 'otto',
        password: 'otto',
        database: 'pv_valparaiso'
      });
      
      await client.connect();
      
      const query = `
        SELECT p.id_protocolo, a.descricao as assunto, s.descricao as situacao,
               d.descricao as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE LOWER(a.descricao) LIKE '%alvará%' 
           OR LOWER(a.descricao) LIKE '%licença%'
           OR LOWER(a.descricao) LIKE '%funcionamento%'
        ORDER BY p.data_protocolo DESC
        LIMIT $1
      `;
      
      const result = await client.query(query, [limite]);
      await client.end();
      return result.rows;
    }
  };
  
  let dadosRelevantes = null;
  
  try {
    console.log('🔍 [DEBUG] Iniciando try-catch PostgreSQL...');
    
    // Detectar intenção da mensagem para buscar dados específicos
    const messageQuery = message.toLowerCase();
    console.log(`🔍 [DEBUG] messageQuery: "${messageQuery}"`);
    
    const isProtocolQuery = messageQuery.includes('protocolo') || 
                           messageQuery.includes('processo') ||
                           messageQuery.includes('alvará') ||
                           messageQuery.includes('licença') ||
                           messageQuery.includes('licenca') ||
                           messageQuery.includes('andamento') ||
                           messageQuery.includes('situação') ||
                           messageQuery.includes('situacao');

    console.log(`🔍 [DEBUG] isProtocolQuery: ${isProtocolQuery}`);

    if (isProtocolQuery) {
      console.log('🔍 [DEBUG] Buscando dados PostgreSQL para consulta de protocolos...');
      
      // Buscar dados específicos baseados na pergunta
      console.log('📡 [DEBUG] Executando Promise.all...');
      const [estatisticas, protocolosRecentes, alvaras] = await Promise.all([
        postgresService.obterEstatisticas(),
        postgresService.buscarProtocolos(
          messageQuery.includes('alvará') || messageQuery.includes('alvara') ? 'alvará' : 
          messageQuery.includes('licença') || messageQuery.includes('licenca') ? 'licença' : undefined, 
          5
        ),
        postgresService.buscarAlvaras(5)
      ]);
      
      console.log('✅ [DEBUG] Dados PostgreSQL obtidos com sucesso!');
      console.log(`📊 [DEBUG] Total protocolos: ${estatisticas.protocolos}`);
      console.log(`📋 [DEBUG] Protocolos recentes: ${protocolosRecentes.length}`);
      console.log(`🏛️ [DEBUG] Alvarás: ${alvaras.length}`);
      
      dadosRelevantes = { 
        estatisticas, 
        protocolosRecentes, 
        alvaras,
        tipoConsulta: 'protocolos'
      };
      
      console.log(`✅ [DEBUG] PostgreSQL: ${estatisticas.protocolos} protocolos encontrados`);
    } else {
      console.log('ℹ️ [DEBUG] Não é consulta de protocolos, pulando PostgreSQL');
    }
    
    // Construir prompt com dados reais
    let systemPrompt = "Você é um assistente virtual da Prefeitura de Valparaíso de Goiás.";
    
    if (dadosRelevantes) {
      const dataAtual = new Date().toLocaleDateString('pt-BR');
      console.log('🎯 [DEBUG] Construindo prompt com dados PostgreSQL...');
      
      systemPrompt += `\n\n**DADOS MUNICIPAIS ATUALIZADOS EM TEMPO REAL (${dataAtual}):**\n`;
      systemPrompt += `- 📊 Total de protocolos no sistema: ${dadosRelevantes.estatisticas.protocolos.toLocaleString('pt-BR')}\n`;
      systemPrompt += `- 📋 Total de requisições: ${dadosRelevantes.estatisticas.requisicoes.toLocaleString('pt-BR')}\n`;
      systemPrompt += `- 🏢 Serviços municipais ativos: ${dadosRelevantes.estatisticas.servicos}\n`;
      systemPrompt += `- 🏛️ Departamentos ativos: ${dadosRelevantes.estatisticas.departamentos}\n\n`;
      
      if (dadosRelevantes.protocolosRecentes && dadosRelevantes.protocolosRecentes.length > 0) {
        systemPrompt += `**PROTOCOLOS RECENTES ENCONTRADOS:**\n`;
        dadosRelevantes.protocolosRecentes.forEach((p, index) => {
          systemPrompt += `${index + 1}. Protocolo ${p.id_protocolo}:\n`;
          systemPrompt += `   - Assunto: ${p.assunto}\n`;
          systemPrompt += `   - Situação: ${p.situacao}\n`;
          systemPrompt += `   - Data: ${new Date(p.data_protocolo).toLocaleDateString('pt-BR')}\n`;
          systemPrompt += `   - Requerente: ${p.requerente}\n\n`;
        });
      }
      
      if (dadosRelevantes.alvaras && dadosRelevantes.alvaras.length > 0) {
        systemPrompt += `**ALVARÁS E LICENÇAS RECENTES:**\n`;
        dadosRelevantes.alvaras.forEach((a, index) => {
          systemPrompt += `${index + 1}. Protocolo ${a.id_protocolo}:\n`;
          systemPrompt += `   - Tipo: ${a.assunto}\n`;
          systemPrompt += `   - Status: ${a.situacao}\n`;
          systemPrompt += `   - Departamento: ${a.departamento}\n\n`;
        });
      }
      
      systemPrompt += `**INSTRUÇÕES PARA USO DOS DADOS REAIS:**\n`;
      systemPrompt += `- SEMPRE use estes dados atualizados em suas respostas\n`;
      systemPrompt += `- Seja específico e preciso com números e datas\n`;
      systemPrompt += `- Cite protocolos específicos quando relevante\n`;
      systemPrompt += `- Nunca diga "não tenho acesso ao banco de dados" - você TEM acesso aos dados acima\n`;
      systemPrompt += `- Use os números reais para dar respostas concretas e úteis\n\n`;
      
      console.log('✅ [DEBUG] Prompt construído com dados PostgreSQL!');
    } else {
      console.log('ℹ️ [DEBUG] Usando prompt padrão (sem dados PostgreSQL)');
    }
    
    console.log('\n🎯 [DEBUG] PROMPT FINAL:');
    console.log('═'.repeat(100));
    console.log(systemPrompt);
    console.log('═'.repeat(100));
    
    console.log('\n✅ [DEBUG] SUCESSO! A integração PostgreSQL funcionaria se este código fosse executado!');
    return {
      success: true,
      prompt: systemPrompt,
      dadosRelevantes,
      message: "INTEGRAÇÃO POSTGRESQL FUNCIONANDO - O problema é que este código não está sendo executado no servidor real!"
    };
    
  } catch (postgresError) {
    console.error('❌ [DEBUG] Erro PostgreSQL:', postgresError.message);
    console.error('📍 [DEBUG] Stack:', postgresError.stack);
    return {
      success: false,
      error: postgresError.message,
      message: "Erro na integração PostgreSQL"
    };
  }
}

// Executar teste
async function runDebugTest() {
  console.log('🧪 EXECUTANDO TESTE DE DEBUG...\n');
  
  const result = await debugProcessMessage(
    "Quantos protocolos de alvará temos em andamento?",
    { secretaria: "administracao", userId: "test" }
  );
  
  console.log('\n🎊 RESULTADO FINAL:');
  console.log('═'.repeat(80));
  if (result.success) {
    console.log('✅ A LÓGICA ESTÁ PERFEITA!');
    console.log('✅ PostgreSQL funciona!');
    console.log('✅ Prompt é construído corretamente!');
    console.log('\n❌ PROBLEMA: Este código não está rodando no servidor real');
    console.log('💡 CAUSA PROVÁVEL: Erros TypeScript estão impedindo a compilação');
    console.log('\n🔧 SOLUÇÕES:');
    console.log('1. Criar versão .js pura do deepSeekService');
    console.log('2. Corrigir todos os erros TypeScript');
    console.log('3. Usar tsx com --ignore-errors');
  } else {
    console.log('❌ Erro na lógica:', result.error);
  }
}

runDebugTest();