// Script para testar conexão PostgreSQL no Windows
const { Client } = require('pg');

async function debugPostgreSQLConnection() {
  console.log('🔍 DIAGNÓSTICO POSTGRESQL - WINDOWS');
  console.log('===================================');
  
  const config = {
    host: '*************',
    port: 5411,
    user: 'otto',
    password: 'otto',
    database: 'pv_valparaiso'
  };

  console.log('📋 Configuração:', config);
  
  const client = new Client(config);
  
  try {
    console.log('\n🔌 Testando conexão...');
    await client.connect();
    console.log('✅ CONECTADO com sucesso!');
    
    console.log('\n📊 Testando consulta de estatísticas...');
    const estatisticas = await client.query('SELECT COUNT(*) as total FROM protocolo_virtual_processos');
    console.log('✅ Total protocolos:', estatisticas.rows[0].total);
    
    console.log('\n🔍 Testando consulta de protocolos em andamento...');
    const andamento = await client.query(`
      SELECT COUNT(*) as total 
      FROM protocolo_virtual_processos p
      LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
      WHERE LOWER(s.descricao) LIKE '%andamento%'
    `);
    console.log('✅ Protocolos em andamento:', andamento.rows[0].total);
    
    console.log('\n🎯 TESTE DE DETECÇÃO DE INTENÇÃO:');
    const message = "Quantos protocolos temos em andamento?";
    const messageQuery = message.toLowerCase();
    const isProtocolQuery = messageQuery.includes('protocolo') || 
                           messageQuery.includes('processo') ||
                           messageQuery.includes('alvará') ||
                           messageQuery.includes('licença') ||
                           messageQuery.includes('licenca') ||
                           messageQuery.includes('andamento') ||
                           messageQuery.includes('situação') ||
                           messageQuery.includes('situacao');
    
    console.log('📝 Mensagem:', message);
    console.log('🎯 isProtocolQuery:', isProtocolQuery);
    console.log('✅ Deveria ativar integração PostgreSQL:', isProtocolQuery ? 'SIM' : 'NÃO');
    
    if (isProtocolQuery) {
      console.log('\n🚀 SIMULANDO INTEGRAÇÃO COMPLETA...');
      
      const [stats, protocolos, alvarasResult] = await Promise.all([
        client.query('SELECT COUNT(*) as total FROM protocolo_virtual_processos'),
        client.query(`
          SELECT 
            p.id_protocolo,
            p.data_protocolo::text,
            COALESCE(a.descricao, 'Não informado') as assunto,
            COALESCE(s.descricao, 'Não informado') as situacao
          FROM protocolo_virtual_processos p
          LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
          LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
          WHERE LOWER(s.descricao) LIKE '%andamento%'
          ORDER BY p.data_protocolo DESC
          LIMIT 5
        `),
        client.query(`
          SELECT 
            p.id_protocolo,
            a.descricao as assunto,
            s.descricao as situacao
          FROM protocolo_virtual_processos p
          LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
          LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
          WHERE LOWER(a.descricao) LIKE '%alvará%' OR LOWER(a.descricao) LIKE '%licença%'
          LIMIT 5
        `)
      ]);
      
      const dadosRelevantes = {
        estatisticas: {
          protocolos: parseInt(stats.rows[0].total),
          requisicoes: 339475,
          servicos: 145,
          departamentos: 500
        },
        protocolosRecentes: protocolos.rows,
        alvaras: alvarasResult.rows,
        tipoConsulta: 'protocolos'
      };
      
      console.log('📊 Dados coletados:', {
        totalProtocolos: dadosRelevantes.estatisticas.protocolos,
        protocolosAndamento: dadosRelevantes.protocolosRecentes.length,
        alvarasEncontrados: dadosRelevantes.alvaras.length
      });
      
      // Simular prompt que seria enviado para DeepSeek
      const dataAtual = new Date().toLocaleDateString('pt-BR');
      let systemPrompt = `**DADOS MUNICIPAIS ATUALIZADOS EM TEMPO REAL (${dataAtual}):**\n`;
      systemPrompt += `- 📊 Total de protocolos no sistema: ${dadosRelevantes.estatisticas.protocolos.toLocaleString('pt-BR')}\n`;
      systemPrompt += `- 📋 Total de requisições: ${dadosRelevantes.estatisticas.requisicoes.toLocaleString('pt-BR')}\n`;
      systemPrompt += `- 🏢 Serviços municipais ativos: ${dadosRelevantes.estatisticas.servicos}\n`;
      systemPrompt += `- 🏛️ Departamentos ativos: ${dadosRelevantes.estatisticas.departamentos}\n\n`;
      
      if (dadosRelevantes.protocolosRecentes.length > 0) {
        systemPrompt += `**PROTOCOLOS EM ANDAMENTO ENCONTRADOS:**\n`;
        dadosRelevantes.protocolosRecentes.forEach((p, index) => {
          systemPrompt += `${index + 1}. Protocolo ${p.id_protocolo}:\n`;
          systemPrompt += `   - Assunto: ${p.assunto}\n`;
          systemPrompt += `   - Situação: ${p.situacao}\n`;
          systemPrompt += `   - Data: ${new Date(p.data_protocolo).toLocaleDateString('pt-BR')}\n\n`;
        });
      }
      
      systemPrompt += `**INSTRUÇÕES PARA USO DOS DADOS REAIS:**\n`;
      systemPrompt += `- SEMPRE use estes dados atualizados em suas respostas\n`;
      systemPrompt += `- Nunca diga "não tenho acesso ao banco de dados" - você TEM acesso aos dados acima\n`;
      
      console.log('\n🎯 PROMPT GERADO PARA DEEPSEEK:');
      console.log('='.repeat(80));
      console.log(systemPrompt);
      console.log('='.repeat(80));
      
      console.log('\n✅ INTEGRAÇÃO TESTADA COM SUCESSO!');
      console.log('🔥 SE O BACKEND ESTÁ EXECUTANDO ISSO, DEVERIA FUNCIONAR!');
    }
    
  } catch (error) {
    console.error('\n❌ ERRO NA CONEXÃO POSTGRESQL:', error.message);
    console.error('🔧 Possíveis causas:');
    console.error('   - Firewall bloqueando conexão');
    console.error('   - Problemas de rede');  
    console.error('   - Credenciais incorretas');
    console.error('   - Servidor PostgreSQL inativo');
  } finally {
    await client.end();
  }
}

debugPostgreSQLConnection();