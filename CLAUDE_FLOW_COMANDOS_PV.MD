# 📚 Claude-Flow para Prefeitura Virtual IA - Guia de Comandos

## 📋 Índice
- [Status do Sistema](#-status-do-sistema)
- [Instalação e Configuração](#-instalação-e-configuração)
- [Modos SPARC para o Projeto](#-modos-sparc-para-o-projeto)
- [Gerenciamento de Memória](#-gerenciamento-de-memória)
- [Exemplos Práticos - Prefeitura Virtual](#-exemplos-práticos---prefeitura-virtual)
- [Fluxos de Trabalho Específicos](#-fluxos-de-trabalho-específicos)

---

## 🔍 Status do Sistema

### ✅ Node.js 22 Instalado
Temos Node.js 22.17.1 instalado via NVM. Podemos usar tanto o simulador quanto o Claude-Flow oficial:

```bash
# Carregar Node.js 22 (via NVM)
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
node --version  # Mostra v22.17.1

# Status do sistema (simulador)
node claude-flow-sim.js status

# Status do sistema (Claude-Flow oficial - quando configurado)
./claude-flow status
```

---

## 🚀 Instalação e Configuração

### Configuração Atual do Projeto
```bash
# Estrutura já criada:
.claude/
├── settings.json         # Configurações otimizadas
└── settings.local.json   # Configurações locais

.roomodes/               # Diretório para modos SPARC
claude-flow-sim.js       # Simulador funcional
.claude-flow-memory.json # Memória persistente
```

### Para Usar o Claude-Flow Oficial (Node.js 22 ✅)
```bash
# Carregar NVM e Node.js 22:
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Instalar e usar Claude-Flow oficial:
npx claude-flow@latest init --sparc
./claude-flow start --ui --port 3000
```

---

## ⭐ Modos SPARC para o Projeto

### Comandos Básicos do Simulador
```bash
# Listar todos os modos disponíveis
node claude-flow-sim.js sparc modes

# Executar modo específico
node claude-flow-sim.js sparc run [modo] "[tarefa]"

# Comando SPARC simples
node claude-flow-sim.js sparc "implementar cache para secretaria de saúde"
```

### Modos Especializados para Prefeitura Virtual

#### 🏗️ Arquitetura e Design
```bash
# Design de novas funcionalidades
node claude-flow-sim.js sparc run architect "adicionar sistema de notificações push para alertas municipais"
node claude-flow-sim.js sparc run architect "design sistema de agendamento online para serviços municipais"
node claude-flow-sim.js sparc run architect "criar arquitetura para integração com sistemas legados da prefeitura"

# Pesquisa e análise
node claude-flow-sim.js sparc run ask "melhores práticas para cache de respostas de IA em sistemas governamentais"
node claude-flow-sim.js sparc run ask "como implementar LGPD em chatbot municipal"
node claude-flow-sim.js sparc run research "análise de custos de IA para municípios pequenos"
```

#### 💻 Desenvolvimento
```bash
# Backend - Novas secretarias
node claude-flow-sim.js sparc run code "implementar endpoints para Secretaria de Cultura e Turismo"
node claude-flow-sim.js sparc run code "adicionar suporte para múltiplos idiomas no chat"
node claude-flow-sim.js sparc run code "criar sistema de métricas detalhadas por secretaria"

# Frontend - Interface do chatbot
node claude-flow-sim.js sparc run code "implementar interface de chat responsiva com React"
node claude-flow-sim.js sparc run code "criar dashboard administrativo para visualizar métricas"
node claude-flow-sim.js sparc run code "adicionar modo dark/light no frontend"

# Otimização de cache
node claude-flow-sim.js sparc run optimize "melhorar hit rate do cache semântico acima de 70%"
node claude-flow-sim.js sparc run optimize "implementar cache warming para perguntas frequentes"
```

#### 🧪 Testes
```bash
# Testes específicos do sistema
node claude-flow-sim.js sparc run tdd "criar testes para sistema de cache multicamada"
node claude-flow-sim.js sparc run tdd "testar integração com DeepSeek API"
node claude-flow-sim.js sparc run tdd "implementar testes de carga para 1000 usuários simultâneos"

# Testes de integração
node claude-flow-sim.js sparc run integration "testar fluxo completo de autenticação com PostgreSQL"
node claude-flow-sim.js sparc run integration "validar integração Redis cache com Bull queues"
```

#### 🔒 Segurança
```bash
# Auditoria de segurança
node claude-flow-sim.js sparc run security-review "auditar sistema JWT e rate limiting"
node claude-flow-sim.js sparc run security-review "verificar conformidade com LGPD"
node claude-flow-sim.js sparc run security-review "analisar logs de segurança por tentativas de invasão"

# Implementação de segurança
node claude-flow-sim.js sparc run security "implementar 2FA para usuários administrativos"
node claude-flow-sim.js sparc run security "adicionar criptografia end-to-end para dados sensíveis"
```

#### 🚀 DevOps e Deploy
```bash
# Configuração de ambiente
node claude-flow-sim.js sparc run devops "configurar Docker containers para todos os serviços"
node claude-flow-sim.js sparc run devops "criar pipeline CI/CD com GitHub Actions"
node claude-flow-sim.js sparc run devops "setup ambiente de staging na AWS"

# Monitoramento
node claude-flow-sim.js sparc run devops "implementar monitoramento com Prometheus e Grafana"
node claude-flow-sim.js sparc run devops "configurar alertas para uso excessivo de tokens IA"
```

---

## 🧠 Gerenciamento de Memória

### Armazenar Informações do Projeto
```bash
# Configurações técnicas
node claude-flow-sim.js memory store tech_stack "Node.js 22.17.1, Express, TypeScript, Redis, PostgreSQL, MongoDB, DeepSeek V3"
node claude-flow-sim.js memory store databases "PostgreSQL: *************:5411, MongoDB: *************:2711"
node claude-flow-sim.js memory store cache_strategy "Cache multicamada: Exact (MD5) + Semantic (AI) + Context"

# Decisões de arquitetura
node claude-flow-sim.js memory store architecture_decision_001 "Usar Redis dual-database: DB0 para cache, DB1 para filas Bull"
node claude-flow-sim.js memory store cost_optimization "Horário de desconto DeepSeek: 13:30-21:30 Brasília (50% economia)"
node claude-flow-sim.js memory store security_policy "JWT com 24h expiration + bcrypt 12 rounds + rate limiting por endpoint"

# Secretarias e domínios
node claude-flow-sim.js memory store secretarias "Administração, Finanças, Saúde, Educação, Obras, Assistência Social, Meio Ambiente"
node claude-flow-sim.js memory store prompts_customizados "7 prompts específicos por secretaria para respostas contextualizadas"

# Métricas e metas
node claude-flow-sim.js memory store performance_target "Cache hit rate: 60-70%, Economia: R$ 540-960/mês"
node claude-flow-sim.js memory store cost_per_message "Normal: $0.00219, Desconto: $0.001095"
```

### Consultar Informações
```bash
# Buscar configurações
node claude-flow-sim.js memory query cache
node claude-flow-sim.js memory query database
node claude-flow-sim.js memory query secretaria

# Ver toda a memória
node claude-flow-sim.js memory list
```

---

## 🎯 Exemplos Práticos - Prefeitura Virtual

### 1. Adicionar Nova Secretaria
```bash
# Passo 1: Armazenar contexto
node claude-flow-sim.js memory store nova_secretaria "Secretaria de Esportes e Lazer"
node claude-flow-sim.js memory store esportes_servicos "Quadras, Piscinas, Eventos esportivos, Escolinhas"

# Passo 2: Planejar arquitetura
node claude-flow-sim.js sparc run architect "design integração da Secretaria de Esportes com sistema existente"

# Passo 3: Implementar backend
node claude-flow-sim.js sparc run code "criar endpoints REST para Secretaria de Esportes"
node claude-flow-sim.js sparc run code "implementar prompts específicos para consultas sobre esportes"

# Passo 4: Testes
node claude-flow-sim.js sparc run tdd "criar testes para nova secretaria de esportes"

# Passo 5: Documentar
node claude-flow-sim.js sparc run document "atualizar documentação com nova secretaria"
```

### 2. Otimizar Performance do Cache
```bash
# Análise inicial
node claude-flow-sim.js sparc run analyze "analisar logs para identificar perguntas mais frequentes"
node claude-flow-sim.js memory store frequent_questions "Lista de top 100 perguntas por secretaria"

# Implementar melhorias
node claude-flow-sim.js sparc run optimize "implementar cache preemptivo para perguntas frequentes"
node claude-flow-sim.js sparc run code "criar job para cache warming às 6h da manhã"

# Validar resultados
node claude-flow-sim.js sparc run analyze "comparar métricas de cache antes e depois da otimização"
```

### 3. Implementar Dashboard Administrativo
```bash
# Design
node claude-flow-sim.js sparc run architect "design dashboard com métricas em tempo real"
node claude-flow-sim.js memory store dashboard_features "Gráficos de uso, custos, cache hit rate, filas"

# Frontend
node claude-flow-sim.js sparc run code "implementar componentes React para gráficos com Chart.js"
node claude-flow-sim.js sparc run code "criar websocket para atualização em tempo real"

# Backend
node claude-flow-sim.js sparc run code "criar endpoints de métricas agregadas por período"
node claude-flow-sim.js sparc run code "implementar sistema de alertas por email"

# Integração
node claude-flow-sim.js sparc run integration "conectar dashboard com Redis, MongoDB e PostgreSQL"
```

### 4. Melhorar Segurança
```bash
# Auditoria
node claude-flow-sim.js sparc run security-review "auditar todos endpoints públicos"
node claude-flow-sim.js memory store security_issues "Lista de vulnerabilidades encontradas"

# Correções
node claude-flow-sim.js sparc run security "implementar validação Zod em todos os endpoints"
node claude-flow-sim.js sparc run security "adicionar helmet.js para headers de segurança"
node claude-flow-sim.js sparc run security "implementar rate limiting mais restritivo"

# Testes de segurança
node claude-flow-sim.js sparc run tdd "criar suite de testes de penetração"
```

---

## 🔄 Fluxos de Trabalho Específicos

### Fluxo Completo: Nova Funcionalidade
```bash
# 1. Pesquisa
node claude-flow-sim.js sparc run ask "melhores práticas para sistema de tickets municipais"

# 2. Salvar decisões
node claude-flow-sim.js memory store feature_tickets "Sistema de tickets para solicitações de serviços"

# 3. Arquitetura
node claude-flow-sim.js sparc run architect "design sistema de tickets integrado ao chat"

# 4. Desenvolvimento iterativo
node claude-flow-sim.js sparc run code "implementar modelo de dados para tickets"
node claude-flow-sim.js sparc run code "criar API REST para gerenciamento de tickets"
node claude-flow-sim.js sparc run code "implementar interface de usuário para tickets"

# 5. Testes
node claude-flow-sim.js sparc run tdd "criar testes unitários e integração para tickets"

# 6. Deploy
node claude-flow-sim.js sparc run devops "configurar deploy do sistema de tickets"
```

### Fluxo de Debugging
```bash
# 1. Identificar problema
node claude-flow-sim.js sparc run debug "investigar por que cache semântico não está funcionando"

# 2. Analisar
node claude-flow-sim.js sparc run analyze "revisar logs de erro do Redis e configurações"

# 3. Corrigir
node claude-flow-sim.js sparc run code "corrigir configuração de conexão Redis para cache semântico"

# 4. Testar
node claude-flow-sim.js sparc run test "validar funcionamento do cache após correção"
```

### Fluxo de Otimização de Custos
```bash
# 1. Análise de custos atual
node claude-flow-sim.js sparc run analyze "analisar custos atuais por secretaria e tipo de pergunta"

# 2. Identificar oportunidades
node claude-flow-sim.js sparc run optimize "identificar queries que podem ser cacheadas mais agressivamente"

# 3. Implementar melhorias
node claude-flow-sim.js sparc run code "aumentar TTL do cache para perguntas sobre horários e endereços"
node claude-flow-sim.js sparc run code "implementar agrupamento de perguntas similares"

# 4. Monitorar resultados
node claude-flow-sim.js memory store cost_reduction "Economia adicional de 15% após otimizações"
```

---

## 💡 Dicas Específicas para o Projeto

### Use a Memória para Contexto
```bash
# Sempre salve decisões importantes
node claude-flow-sim.js memory store decision_$(date +%Y%m%d) "Decisão tomada hoje"

# Mantenha registro de problemas resolvidos
node claude-flow-sim.js memory store solved_issue_001 "Cache Redis desconectando - resolvido aumentando timeout"

# Documente padrões do projeto
node claude-flow-sim.js memory store pattern_auth "JWT no header Authorization como Bearer token"
```

### Comandos Mais Úteis para o Dia a Dia
```bash
# Quando adicionar nova feature
node claude-flow-sim.js sparc run architect "design [sua feature]"
node claude-flow-sim.js sparc run code "implement [sua feature]"
node claude-flow-sim.js sparc run tdd "test [sua feature]"

# Quando encontrar bugs
node claude-flow-sim.js sparc run debug "investigate [seu problema]"
node claude-flow-sim.js sparc run analyze "analyze [logs/código]"

# Para melhorias
node claude-flow-sim.js sparc run optimize "optimize [componente]"
node claude-flow-sim.js sparc run refactor "refactor [código]"
```

---

## 📝 Notas Finais

1. **Node.js 22 Instalado**: Temos Node.js 22.17.1 via NVM - podemos usar Claude-Flow oficial
2. **Simulador Disponível**: Mantemos o simulador (`claude-flow-sim.js`) como alternativa funcional
3. **Memória Persistente**: Toda informação salva fica em `.claude-flow-memory.json`
4. **Contexto do Projeto**: Sempre inclua contexto específico da Prefeitura Virtual nos comandos
5. **Economia**: Foque em comandos que ajudem a manter/melhorar a economia de 60-70%

### 🚀 Para Migrar para Claude-Flow Oficial:
```bash
# Sempre carregue o NVM primeiro:
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Então use os comandos oficiais:
npx claude-flow@latest init --sparc
./claude-flow start --ui --port 3000
```

**Lembre-se**: O Claude-Flow é uma ferramenta para acelerar desenvolvimento. Use para planejar, implementar e otimizar o sistema de chatbot municipal!